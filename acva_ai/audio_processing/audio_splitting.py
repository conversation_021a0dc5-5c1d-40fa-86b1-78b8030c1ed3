from typing import Optional, List

from pydub import AudioSegment, silence
import os
import shutil
from acva_ai._params import SILENCE_THRESHOLD_STARTING_DELTA, SILENCE_THRESHOLD_INCREMENT, MIN_SILENCE_LEN_SECONDS


def split_audio_segment_by_silence(
        audio_segment: AudioSegment,
        max_duration_seconds: float = 10 * 60,  # 10 minutes
        min_silence_len_seconds: float = MIN_SILENCE_LEN_SECONDS,  # minimum silence length to split on, in seconds
        silence_thresh: Optional[int] = None,  # silence threshold in dBFS
        silence_threshold_starting_delta: int = SILENCE_THRESHOLD_STARTING_DELTA,  # dBFS decrement for silence threshold
        silence_threshold_increment: int = SILENCE_THRESHOLD_INCREMENT  # dBFS increment for more aggressive silence detection
) -> List[AudioSegment]:
    """
    Splits an AudioSegment into smaller chunks at points of detected silence.

    The function aims to break the audio into pieces that:
    - Do not exceed `max_duration_seconds`.
    - Are split at points where silence is detected (using `pydub`'s `split_on_silence`).

    If a segment exceeds the max duration even after splitting, the function recursively
    tries to split it further using shorter silence detection windows and a higher
    silence threshold (i.e., becoming more aggressive in finding silence).

    Parameters:
    ----------
    audio : AudioSegment
        The input audio to split.

    max_duration_seconds : float, optional
        The maximum allowed duration (in seconds) for any resulting segment.
        Defaults to 10 minutes (600 seconds).

    min_silence_len_seconds : float, optional
        The minimum length of silence (in seconds) that qualifies as a split point.
        Defaults to 1.0 second. Longer values will produce fewer, larger segments;
        shorter values may split on brief pauses, creating smaller chunks.

    silence_thresh : int, optional
        The silence threshold in dBFS (decibels relative to full scale).
        Any part of the audio quieter than this threshold is considered silence.
        If None (default), it is set to (average loudness - 16 dB), i.e.:
        `audio.dBFS - 16`.

        Intuition:
        - dBFS is a relative measure where 0 dBFS is the maximum possible digital level.
        - Typical loud speech might be around -20 dBFS; quiet background noise might be
          around -40 dBFS.
        - A threshold like `-40` means "consider anything quieter than -40 dBFS as silence".
        - The higher (closer to 0) the threshold (e.g., `-30`), the more aggressive it is
          in detecting silence (it will treat quiet parts as silence).
        - The lower the threshold (e.g., `-50`), the more forgiving (only very quiet parts
          are treated as silence).

    Returns:
    -------
    List[AudioSegment]
        A list of audio chunks where each chunk:
        - is no longer than `max_duration_seconds`
        - has been split at silence points where possible.

    Notes:
    -----
    - The function is recursive: if chunks are still too long after initial splitting,
      it will try again with more aggressive settings (shorter min_silence_len and a
      higher silence_thresh).
    - `keep_silence=250` ms is added to each side of the split point to make the cut less abrupt.
    - This function does not modify or process audio beyond splitting.
    """

    max_duration_ms = int(max_duration_seconds * 1000)
    min_silence_len = int(min_silence_len_seconds * 1000)

    if len(audio_segment) <= max_duration_ms:
        return [audio_segment]

    if silence_thresh is None:
        silence_thresh = audio_segment.dBFS - silence_threshold_starting_delta

    chunks = silence.split_on_silence(
        audio_segment,
        min_silence_len=min_silence_len,
        silence_thresh=silence_thresh,
        keep_silence=200
    )

    if not chunks:
        chunks = [audio_segment]

    final_segments = []

    for chunk in chunks:
        if len(chunk) > max_duration_ms:
            tighter_min_silence_len_seconds = max(0.2, min_silence_len_seconds / 2)
            tighter_thresh = silence_thresh + silence_threshold_increment
            sub_chunks = split_audio_segment_by_silence(
                chunk,
                max_duration_seconds=max_duration_seconds,
                min_silence_len_seconds=tighter_min_silence_len_seconds,
                silence_thresh=tighter_thresh
            )
            final_segments.extend(sub_chunks)
        else:
            final_segments.append(chunk)

    return final_segments


def merge_audio_chunks_by_duration(
        chunks: List[AudioSegment],
        min_duration_seconds: float,
        max_duration_seconds: float
) -> List[AudioSegment]:
    """
    Merge adjacent audio chunks so that each merged chunk is at least min_duration_seconds and at most max_duration_seconds.
    If a single chunk is already longer than max_duration_seconds, it is left as is.
    """
    merged_chunks = []
    current_chunk = None
    min_ms = int(min_duration_seconds * 1000)
    max_ms = int(max_duration_seconds * 1000)

    for chunk in chunks:
        if current_chunk is None:
            current_chunk = chunk
        else:
            # Try to merge
            if len(current_chunk) + len(chunk) <= max_ms:
                current_chunk += chunk
            else:
                # If current_chunk is too short, but adding next would exceed max, still yield it as is
                merged_chunks.append(current_chunk)
                current_chunk = chunk
    if current_chunk is not None:
        merged_chunks.append(current_chunk)

    # Second pass: try to ensure all chunks are at least min_ms, except the last one
    final_chunks = []
    buffer = None
    for chunk in merged_chunks:
        if buffer is None:
            buffer = chunk
        else:
            if len(buffer) < min_ms:
                # Merge with next chunk if possible
                if len(buffer) + len(chunk) <= max_ms:
                    buffer += chunk
                else:
                    final_chunks.append(buffer)
                    buffer = chunk
            else:
                final_chunks.append(buffer)
                buffer = chunk
    if buffer is not None:
        final_chunks.append(buffer)
    return final_chunks


def test():
    sample_audio_path = ".data/demo/1e4cd7fa-b4a1-47ce-99aa-abdf66885b2b.wav"
    output_dir = ".data/tmp/audio_split_test"

    # Clear and recreate output directory
    if os.path.exists(output_dir):
        shutil.rmtree(output_dir)
    os.makedirs(output_dir, exist_ok=True)

    # Load audio
    audio_segment = AudioSegment.from_file(sample_audio_path)

    # Print original duration
    original_duration_sec = len(audio_segment) / 1000
    print(f"Original audio duration: {original_duration_sec:.2f} seconds")

    # Split
    result = split_audio_segment_by_silence(
        audio_segment=audio_segment,
        max_duration_seconds=10 * 60
    )

    # Print and save chunks
    for i, chunk in enumerate(result):
        duration_sec = len(chunk) / 1000
        print(f"Chunk {i+1}: {duration_sec:.2f} seconds")
        chunk_path = os.path.join(output_dir, f"chunk_{i+1}.wav")
        chunk.export(chunk_path, format="wav")

    print(f"Saved {len(result)} chunks to {output_dir}")