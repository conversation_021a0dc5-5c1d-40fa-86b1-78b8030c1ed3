import asyncio
import logging
import time
from typing import Dict, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class RateLimitConfig:
    """Configuration for rate limits"""
    tokens_per_minute: int
    requests_per_minute: int
    
    @property
    def tokens_per_second(self) -> float:
        return self.tokens_per_minute / 60.0
    
    @property
    def requests_per_second(self) -> float:
        return self.requests_per_minute / 60.0


class TokenBucket:
    """Token bucket algorithm implementation for rate limiting"""
    
    def __init__(self, rate_per_second: float, max_capacity: float):
        """
        Initialize token bucket
        
        Args:
            rate_per_second: Rate at which tokens are added to the bucket per second
            max_capacity: Maximum capacity of the bucket
        """
        self.rate_per_second = rate_per_second
        self.max_capacity = max_capacity
        self.tokens = max_capacity  # Start with a full bucket
        self.last_refill_time = time.time()
        self.lock = asyncio.Lock()
    
    async def _refill(self) -> None:
        """Refill the bucket based on elapsed time"""
        now = time.time()
        elapsed = now - self.last_refill_time
        
        # Calculate tokens to add based on elapsed time
        new_tokens = elapsed * self.rate_per_second
        
        # Update token count and timestamp
        self.tokens = min(self.tokens + new_tokens, self.max_capacity)
        self.last_refill_time = now
    
    async def consume(self, tokens: float) -> float:
        """
        Try to consume tokens from the bucket
        
        Args:
            tokens: Number of tokens to consume
            
        Returns:
            Wait time in seconds if not enough tokens, 0 if tokens were consumed
        """
        async with self.lock:
            await self._refill()
            
            # If we have enough tokens, consume them and return 0 wait time
            if self.tokens >= tokens:
                self.tokens -= tokens
                return 0
            
            # Calculate wait time needed for enough tokens
            deficit = tokens - self.tokens
            wait_time = deficit / self.rate_per_second
            
            # Return wait time (caller will need to wait and try again)
            return wait_time


class AzureOpenAIRateLimiter:
    """Rate limiter for Azure OpenAI API calls"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls, *args, **kwargs):
        """Singleton pattern to ensure only one rate limiter exists"""
        if cls._instance is None:
            cls._instance = super(AzureOpenAIRateLimiter, cls).__new__(cls)
        return cls._instance
    
    def __init__(self, config: Optional[Dict[str, RateLimitConfig]] = None):
        """
        Initialize rate limiter with model-specific configurations
        
        Args:
            config: Dictionary mapping model IDs to RateLimitConfig objects
        """
        if AzureOpenAIRateLimiter._initialized:
            return
            
        self.model_configs = config or {}
        
        # Default config for models not explicitly configured
        self.default_config = RateLimitConfig(
            tokens_per_minute=250000,  # 250k tokens per minute
            requests_per_minute=1500,  # 1500 requests per minute
        )
        
        # Create token buckets for each limit type
        self.token_buckets: Dict[str, TokenBucket] = {}
        self.request_buckets: Dict[str, TokenBucket] = {}
        
        # Initialize buckets for configured models
        for model_id, config in self.model_configs.items():
            self._init_buckets_for_model(model_id, config)
            
        AzureOpenAIRateLimiter._initialized = True
        
    def _init_buckets_for_model(self, model_id: str, config: RateLimitConfig) -> None:
        """Initialize token and request buckets for a model"""
        # Create token bucket with 80% of max capacity to leave headroom
        self.token_buckets[model_id] = TokenBucket(
            rate_per_second=config.tokens_per_second,
            max_capacity=config.tokens_per_minute * 0.8
        )
        
        # Create request bucket with 80% of max capacity to leave headroom
        self.request_buckets[model_id] = TokenBucket(
            rate_per_second=config.requests_per_second,
            max_capacity=config.requests_per_minute * 0.8
        )
    
    def get_config_for_model(self, model_id: str) -> RateLimitConfig:
        """Get rate limit configuration for a model"""
        if model_id not in self.model_configs:
            # If model not configured, add it with default config
            self.model_configs[model_id] = self.default_config
            self._init_buckets_for_model(model_id, self.default_config)
        
        return self.model_configs[model_id]
    
    async def check_limits(
        self, 
        model_id: str, 
        estimated_tokens: int = 1000
    ) -> float:
        """
        Check if the request would exceed rate limits and return wait time
        
        Args:
            model_id: The model ID to check limits for
            estimated_tokens: Estimated token usage for this request
            
        Returns:
            Wait time in seconds if limits would be exceeded, 0 otherwise
        """
        # Ensure buckets exist for this model
        if model_id not in self.token_buckets:
            config = self.get_config_for_model(model_id)
        
        # Check token limit
        token_wait = await self.token_buckets[model_id].consume(estimated_tokens)
        
        # Check request limit
        request_wait = await self.request_buckets[model_id].consume(1)
        
        # Return the longer wait time
        return max(token_wait, request_wait)
    
    async def wait_if_needed(
        self, 
        model_id: str, 
        estimated_tokens: int = 1000,
        task_id: Optional[str] = None
    ) -> None:
        """
        Wait if necessary to comply with rate limits
        
        Args:
            model_id: The model ID to check limits for
            estimated_tokens: Estimated token usage for this request
            task_id: Optional task ID for logging
        """
        log_prefix = f"[Task {task_id}] " if task_id else ""
        
        wait_time = await self.check_limits(model_id, estimated_tokens)
        
        if wait_time > 0:
            logger.info(
                f"{log_prefix}Rate limit approaching for {model_id}, waiting {wait_time:.2f}s "
                f"to avoid exceeding limits"
            )
            await asyncio.sleep(wait_time)