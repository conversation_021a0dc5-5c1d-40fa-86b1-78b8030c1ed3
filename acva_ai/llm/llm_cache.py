import hashlib
import os
import uuid
from typing import Optional

from acva_ai._params import CACHE_DIR


LLM_CALLS_CACHE_DIR = os.path.join(CACHE_DIR, "openai")
os.makedirs(LLM_CALLS_CACHE_DIR, exist_ok=True)
def _generate_llm_cache_filename(
    prompt: str, model_id: str, max_tokens: int = 1000
) -> str:
    """
    Generate a stable filename for the given LLM call parameters.
    """
    unique_str = f"{prompt}-{model_id}-{max_tokens}"
    cache_uuid = uuid.uuid5(uuid.NAMESPACE_URL, unique_str)
    return os.path.join(LLM_CALLS_CACHE_DIR, f"{cache_uuid}.txt")


LLM_AUDIO_CACHE_DIR = os.path.join(CACHE_DIR, 'openai_audio')


def _generate_audio_cache_filename(file_path: str, model_id: str, language: Optional[str] = None) -> str:
    """
    Generate a unique cache filename based on audio file content and parameters.
    """
    # Create a hash based on file content and parameters
    with open(file_path, 'rb') as f:
        file_content = f.read()
    
    cache_key = f"{hashlib.md5(file_content).hexdigest()}_{model_id}_{language or 'auto'}"
    cache_filename = f"audio_transcription_{cache_key}.txt"
    return os.path.join(LLM_AUDIO_CACHE_DIR, cache_filename)
