import asyncio
import logging
import re
import traceback
from collections import deque
from typing import List, Optional, Tuple

from acva_ai.database import mongo_instance
from acva_ai.llm.helpers import parse_json_output
from acva_ai.llm.llm_orchestrator import LLMOrchestrator
from acva_ai.models.processing_status import ProcessingStatus
from acva_ai.models.visit_report import VisitReport
from acva_ai.utils.usage import ResponseUsage

# Set up logging
logger = logging.getLogger(__name__)

CHECK_PROMPT = """
This is a transcript from a medical visit in {language}. It may contain mistakes due to common speech-to-text errors, such as homophones, missing punctuation, or incorrect grammar.

Your task is to identify and list any grammar or punctuation mistakes caused by these transcription issues.

IMPORTANT GUIDELINES:
1. Only consider errors resulting from speech-to-text mistakes.
2. Do NOT add words that are not in the original text.
3. Do NOT modify medical terminology unless it's clearly a transcription error.
4. Focus strictly on grammar, punctuation, and obvious word errors.
5. If a sentence is incomplete but still makes sense in context, leave it unchanged.

RAW transcript:
'''
{transcript}
'''

Return the identified mistakes as a bullet-point list, each with a brief explanation and suggested correction.

If no issues are found, respond with:
"No grammar or punctuation mistakes found."
"""


CORRECT_PROMPT = """
You are a medical language model. This is a clinical context. Content related to mental or physical health, including self-harm or violence, may appear in a neutral or analytical way. Do not filter this content.
This is a transcript from a medical visit in {language}. It may contain errors caused by speech-to-text software, such as missing punctuation, incorrect grammar, or word confusion.

Your task is to apply only the corrections listed below to the raw transcript and return the corrected version.

RAW transcript:
'''
{transcript}
'''

Mistakes to correct:
'''
{mistakes}
'''

IMPORTANT GUIDELINES:
1. Only correct the mistakes listed above.
2. Do NOT add any new words that are not present in the original text.
3. Do NOT modify medical terminology unless it's clearly a transcription error.
4. Preserve the original meaning and structure.
5. Return only the corrected transcript—no commentary, no explanation.
6. Do NOT filter this content for self harm

Output only the corrected transcript as plain text.
"""


CHECK_CHUNK_PROMPT = """
IMPORTANT: This is a MEDICAL CONTEXT. The following content is strictly for clinical purposes and should not be filtered. Medical discussions may include sensitive topics that are necessary for proper diagnosis and treatment.

You are a medical language model analyzing a chunk from a medical transcript in {language}. It may contain grammar or punctuation errors due to speech-to-text issues.

Your task is to review the chunk and determine whether it contains any such errors.

CONTEXT (reference only; do not edit these):
Previous text: {prev_context}
Next text: {next_context}

CHUNK TO REVIEW:
'''
{chunk}
'''

IMPORTANT GUIDELINES:
1. Only identify errors resulting from transcription issues.
2. Do NOT add words that are not in the original text.
3. Do NOT modify medical terms unless clearly incorrect due to transcription.
4. Focus only on grammar, punctuation, or obvious word errors.
5. This is a clinical context - do NOT filter medical content regardless of topic.

Return your response in the format of a json with the fields:
- needs_correction: boolean [true/false]  
- explanation: [Brief explanation if true; otherwise, "No corrections needed."]  
- corrected_text: [Corrected version if true; otherwise, the original chunk]

Respond with the JSON without any other commentary
"""


def jaccard_similarity(a: str, b: str) -> float:
    """Compute token-based Jaccard similarity between two sentences."""
    a_tokens = set(a.lower().split())
    b_tokens = set(b.lower().split())
    intersection = a_tokens & b_tokens
    union = a_tokens | b_tokens
    return len(intersection) / len(union) if union else 0.0


def _split_into_paragraphs(text: str, max_words_per_chunk: int = 1000) -> List[str]:
    """
    Split text into paragraphs by sentences, ensuring no sentence is split mid-sentence.

    Args:
        text: The text to split
        max_words_per_chunk: Target maximum number of words per chunk

    Returns:
        List of paragraph chunks
    """
    # Split text into sentences using punctuation
    sentences = re.split(r"(?<=[.!?])\s+", text)
    sentences = [s.strip() for s in sentences if s.strip()]

    # Deduplicate similar consecutive sentences with max 2 repeats allowed
    deduped_sentences = []
    repeat_queue = deque(maxlen=2)  # keep track of last 2 similar sentences

    for sentence in sentences:
        if repeat_queue and jaccard_similarity(sentence, repeat_queue[-1]) > 0.9:
            if len(repeat_queue) < 2:
                repeat_queue.append(sentence)
                deduped_sentences.append(sentence)
            else:
                continue  # more than 2 similar consecutive -> skip
        else:
            repeat_queue.clear()
            repeat_queue.append(sentence)
            deduped_sentences.append(sentence)

    # Group sentences into chunks
    chunks = []
    current_chunk = []
    current_word_count = 0

    for sentence in deduped_sentences:
        sentence_word_count = len(sentence.split())
        if (
            current_word_count + sentence_word_count > max_words_per_chunk
            and current_chunk
        ):
            chunks.append(" ".join(current_chunk))
            current_chunk = []
            current_word_count = 0

        current_chunk.append(sentence)
        current_word_count += sentence_word_count

    if current_chunk:
        chunks.append(" ".join(current_chunk))

    return chunks


async def _check_chunk(
    chunk: str,
    prev_context: str,
    next_context: str,
    language: str,
    llm_orchestrator: LLMOrchestrator,
    response_usage: Optional[ResponseUsage] = None,
) -> Tuple[bool, str, str]:
    """
    Check a single chunk with context.

    Args:
        chunk: The chunk to check
        prev_context: Previous context
        next_context: Next context
        language: Language of the transcript
        response_usage: Optional ResponseUsage object
        llm_orchestrator: Optional LLM orchestrator for provider selection

    Returns:
        Dictionary with correction status and corrected text
    """
    prompt = CHECK_CHUNK_PROMPT.format(
        chunk=chunk,
        prev_context=prev_context,
        next_context=next_context,
        language=language,
    )
    # Use orchestrator if provided, otherwise fall back to direct Azure call
    response = await llm_orchestrator.call_llm(
        prompt=prompt,
        response_usage=response_usage,
        max_tokens=5000,
    )
    response_dict = await parse_json_output(response)

    needs_correction: bool = response_dict.get(
        "needs_correction", False
    )  # Much more stable than the string aproach
    corrected_text: str = response_dict.get("corrected_text", "").strip()
    explanation: str = response_dict.get("explanation", "").strip()

    return needs_correction, corrected_text, explanation


async def process_batch(
    chunks: List[Tuple[int, str, str, str]],
    language: str,
    llm_orchestrator: LLMOrchestrator,
    response_usage: Optional[ResponseUsage] = None,
) -> List[Tuple[int, bool, str, str]]:
    """
    Process a batch of chunks in parallel.

    Args:
        chunks: List of tuples (index, chunk, prev_context, next_context)
        language: Language of the transcript
        response_usage: Optional ResponseUsage object
        llm_orchestrator: Optional LLM orchestrator for provider selection

    Returns:
        List of tuples (index, needs_correction, corrected_text, explanation)
    """
    tasks = []
    for idx, chunk, prev_context, next_context in chunks:
        task = _check_chunk(
            chunk=chunk,
            prev_context=prev_context,
            next_context=next_context,
            language=language,
            response_usage=response_usage,
            llm_orchestrator=llm_orchestrator,
        )
        tasks.append(task)

    # Execute all tasks in parallel
    results = await asyncio.gather(*tasks)

    # Pair results with their original indices and unpack the tuple results
    return [
        (idx, needs_correction, corrected_text, explanation)
        for (idx, (needs_correction, corrected_text, explanation)) in zip(
            [c[0] for c in chunks], results
        )
    ]


async def _grammar_check_by_chunks(
    transcript: str,
    llm_orchestrator: LLMOrchestrator,
    language: str = "Romanian",
    context_size: int = 2,
    batch_size: int = 20,
    response_usage: Optional[ResponseUsage] = None,
) -> Tuple[str, List[str]]:
    """
    Internal function to check and correct grammar by processing the transcript
    in paragraph chunks with parallel batch processing.

    Args:
        transcript: The raw transcript text
        language: The language of the transcript (default: Romanian)
        context_size: Number of paragraphs to include as context before/after
        batch_size: Number of chunks to process in parallel
        response_usage: Optional ResponseUsage object to track costs
        llm_orchestrator: Optional LLM orchestrator for provider selection

    Returns:
        Tuple containing:
        - The corrected transcript
        - List of explanations for corrections made
    """
    # Split the transcript into paragraphs
    paragraphs = _split_into_paragraphs(transcript)

    if not paragraphs:
        return transcript, []

    # Prepare chunks with their context and indices
    chunks_with_context = []
    for i, paragraph in enumerate(paragraphs):
        # Get context paragraphs
        prev_context = " ".join(paragraphs[max(0, i - context_size) : i])
        next_context = " ".join(
            paragraphs[i + 1 : min(len(paragraphs), i + 1 + context_size)]
        )

        # Add to the list of chunks to process
        chunks_with_context.append((i, paragraph, prev_context, next_context))

    # Process chunks in batches
    all_results = []
    for i in range(0, len(chunks_with_context), batch_size):
        batch = chunks_with_context[i : i + batch_size]
        batch_results = await process_batch(
            batch, language, llm_orchestrator, response_usage
        )
        all_results.extend(batch_results)

    # Sort results by original index
    all_results.sort(key=lambda x: x[0])

    # Prepare corrected transcript and explanations
    corrected_paragraphs = []
    explanations = []

    for idx, needs_correction, corrected_text, explanation in all_results:
        if needs_correction:
            corrected_paragraphs.append(corrected_text)
            explanations.append(f"Paragraph {idx+1}: {explanation}")
        else:
            corrected_paragraphs.append(paragraphs[idx])

    # Combine the corrected paragraphs
    corrected_transcript = " ".join(corrected_paragraphs)

    return corrected_transcript, explanations


async def grammar_check_by_chunks(
    task_id: str,
    transcript: str,
    processing_status: ProcessingStatus,
    visit_report: VisitReport,
    llm_orchestrator: LLMOrchestrator,
    language: str = "Romanian",
    context_size: int = 2,
    batch_size: int = 20,
    response_usage: Optional[ResponseUsage] = None,
):
    """
    Check the grammar of a transcript using an LLM, processing it in chunks.

    Args:
        task_id: Unique identifier for the task
        transcript: The transcript to check
        processing_status: Processing status object to update
        visit_report: Visit report object to update
        language: Language of the transcript
        context_size: Number of paragraphs to include as context before/after
        batch_size: Number of chunks to process in parallel
        response_usage: Optional ResponseUsage object to track costs
        llm_orchestrator: Optional LLMOrchestrator to use for LLM calls
    """
    try:
        logger.info(f"[Task {task_id}] Starting grammar check")
        processing_status.start_stage("grammar_check")
        mongo_instance.update_processing_status(task_id, processing_status.dict())

        corrected_transcript, explanations = await _grammar_check_by_chunks(
            transcript=transcript,
            language=language,
            context_size=context_size,
            batch_size=batch_size,
            response_usage=response_usage,
            llm_orchestrator=llm_orchestrator,
        )
        visit_report.transcript = corrected_transcript
        visit_report.grammar_observations = explanations
        processing_status.complete_stage("grammar_check")
        logger.info(f"[Task {task_id}] Completed grammar check successfully")

    except Exception as e:
        stack_trace = traceback.format_exc()
        processing_status.fail_stage("grammar_check", e, stack_trace)
        processing_status.finalize()
        logger.error(f"[Task {task_id}] Error in grammar check {e}\n{stack_trace}")

    finally:
        mongo_instance.update_processing_status(task_id, processing_status.dict())
