import asyncio
import json
import logging
import traceback
from typing import Dict, List, Optional, Tuple

from acva_ai.database import mongo_instance
from acva_ai.models.processing_status import ProcessingStatus
from acva_ai.models.visit_report import VisitReport
from acva_ai.utils.usage import ResponseUsage
from acva_ai.llm.llm_orchestrator import LLMOrchestrator

logger = logging.getLogger(__name__)

VERIFICATION_PROMPT = """
You are analyzing a medical transcript in {language} to determine if a specific topic is mentioned.

TRANSCRIPT:
```
{transcript}
```

QUESTION: Is the topic '{section_key}' mentioned or discussed in any way in this transcript?

Respond with only a single word: 'true' if the topic is mentioned, or 'false' if it is not.
"""


async def extract_medical_report_section(
    transcript: str,
    section_key: str,
    section_prompt: str,
    llm_orchestrator: LLMOrchestrator,
    language: str = "Romanian",
    response_usage: Optional[ResponseUsage] = None,
) -> Tuple[str, Optional[str]]:
    """
    Extract a specific section of the medical report from the transcript.

    Args:
        transcript: The medical conversation transcript
        section_key: The section key (e.g., "DIAGNOSTIC", "PLAN_DE_TRATAMENT")
        section_prompt: The prompt template for this section
        language: The language of the transcript (default: Romanian)
        response_usage: Optional ResponseUsage object to track API usage
        llm_orchestrator: Optional LLM orchestrator for provider selection

    Returns:
        Tuple of (section_key, extracted_information as plain text)
    """
    if response_usage is None:
        response_usage = ResponseUsage()

    # Handle empty transcript case
    if not transcript or transcript.strip() == "":
        logger.warning(f"Empty transcript provided for section {section_key}")
        return section_key, None

    # First check if this section is mentioned in the transcript
    verification_prompt = VERIFICATION_PROMPT.format(
        transcript=transcript, section_key=section_key, language=language
    )

    try:
        verification_response = await llm_orchestrator.call_llm(
            prompt=verification_prompt,
            response_usage=response_usage,
        )
        # Parse the verification response - simplified for boolean extraction
        verification_response = verification_response.strip().lower()
        is_present = "true" in verification_response

        logger.debug(
            f"Verification for {section_key}: {is_present} (raw response: {verification_response})"
        )

        # If the section is present, extract the information
        if is_present:
            formatted_prompt = section_prompt.format(
                transcript=transcript,
            )

            extraction_response = await llm_orchestrator.call_llm(
                prompt=formatted_prompt,
                response_usage=response_usage,
            )

            # Return the raw text response without parsing
            return section_key, extraction_response.strip()
        else:
            return section_key, None

    except Exception as e:
        logger.error(f"Error processing section {section_key}: {str(e)}")
        return section_key, None


async def _build_medical_report(
    transcript: str,
    llm_orchestrator: LLMOrchestrator,
    language: str = "Romanian",
    response_usage: Optional[ResponseUsage] = None,
) -> Dict[str, str]:
    """
    Internal function to build a structured medical report from the transcript.

    Args:
        transcript: The medical conversation transcript
        language: The language of the transcript (default: Romanian)
        response_usage: Optional ResponseUsage object to track API usage
        llm_orchestrator: Optional LLM orchestrator for provider selection

    Returns:
        Dictionary containing structured medical report sections
    """
    # Import here to avoid circular imports
    from acva_ai.utils.prompts import prompt_templates

    if response_usage is None:
        response_usage = ResponseUsage()

    # Process all sections concurrently
    tasks = [
        extract_medical_report_section(
            transcript=transcript,
            section_key=key,
            section_prompt=prompt_template,
            llm_orchestrator=llm_orchestrator,
            language=language,
            response_usage=response_usage,
        )
        for key, prompt_template in prompt_templates.items()
    ]
    results = await asyncio.gather(*tasks)

    # Combine results into a single dictionary
    responses = {key: value for key, value in results}

    return responses


async def build_medical_report_with_pipeline(
    task_id: str,
    transcript: str,
    processing_status: Optional[ProcessingStatus],
    visit_report: Optional[VisitReport],
    llm_orchestrator: LLMOrchestrator,
    language: str = "Romanian",
    response_usage: Optional[ResponseUsage] = None,
):
    """
    Processes a transcript and generates a structured medical report.

    Args:
        task_id: Unique identifier for the task
        transcript: The transcript to process
        processing_status: Processing status object to update
        visit_report: Visit report object to update
        language: Language of the transcript
        response_usage: Optional ResponseUsage object to track costs
        llm_orchestrator: Optional LLMOrchestrator to use for LLM calls
    """
    logger.info(f"[Task {task_id}] Starting medical report generation")
    processing_status.start_stage("medical_report")
    mongo_instance.update_processing_status(task_id, processing_status.dict())
    try:
        medical_report = await _build_medical_report(
            transcript=transcript,
            llm_orchestrator=llm_orchestrator,
            language=language,
            response_usage=response_usage,
        )
        visit_report.medical_report = medical_report
        processing_status.complete_stage("medical_report")
        logger.info(
            f"[Task {task_id}] Completed medical report generation successfully"
        )

    except Exception as e:
        stack_trace = traceback.format_exc()
        processing_status.fail_stage("medical_report", e, stack_trace)
        processing_status.finalize()
        logger.error(
            f"[Task {task_id}] Error in medical report generation {e}\n{stack_trace}"
        )

    finally:
        mongo_instance.update_processing_status(task_id, processing_status.dict())
