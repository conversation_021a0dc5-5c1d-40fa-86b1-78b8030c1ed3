import asyncio
import hashlib
import io
import json
import os
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, Optional

import aiohttp
from pydub import AudioSegment

from acva_ai._params import (
    AZURE_OPENAI_TRANSCRIPTION_ENDPOINT,
    AZURE_TRANSCRIPTION_API_VERSION,
    OPENAI_MAX_AUDIO_SIZE,
    OPENAI_TRANSCRIPTION_API_KEY,
    OPENAI_TRANSCRIPTION_MODEL_ID,
)
from acva_ai.llm.llm_cache import LLM_AUDIO_CACHE_DIR
from acva_ai.utils.general_utils import calculate_cost
from acva_ai.utils.usage import LLMUsage, ResponseUsage

os.makedirs(LLM_AUDIO_CACHE_DIR, exist_ok=True)


class AudioTranscriptionError(Exception):
    """Custom exception for audio transcription errors."""

    pass


class AudioFileError(Exception):
    """Custom exception for audio file related errors."""

    pass


@dataclass
class TranscriptionRequest:
    """Data class for transcription request parameters with validation."""

    model_id: str = OPENAI_TRANSCRIPTION_MODEL_ID
    language: Optional[str] = None
    response_format: str = "json"
    temperature: float = 0
    use_cache: bool = True
    response_usage: Optional[ResponseUsage] = None
    max_file_size_mb: float = 25.0  # Maximum allowed file size in MB

    def __post_init__(self):
        """Validate request parameters."""
        if not isinstance(self.model_id, str) or not self.model_id.strip():
            raise AudioTranscriptionError("model_id must be a non-empty string")

        if self.temperature < 0 or self.temperature > 1:
            raise AudioTranscriptionError("temperature must be between 0 and 1")

        if self.response_format not in ["json", "text", "srt", "verbose_json", "vtt"]:
            raise AudioTranscriptionError(
                f"Invalid response_format: {self.response_format}"
            )

        if self.language is not None and not isinstance(self.language, str):
            raise AudioTranscriptionError("language must be a string or None")

        if self.max_file_size_mb <= 0:
            raise AudioTranscriptionError("max_file_size_mb must be positive")


def _estimate_audio_segment_size_mb(audio_segment: AudioSegment) -> float:
    """
    Estimate the file size of an AudioSegment when exported as WAV.

    WAV file size calculation:
    - Header: 44 bytes (standard WAV header)
    - Audio data: duration_seconds * sample_rate * channels * bits_per_sample / 8

    Args:
        audio_segment: AudioSegment object

    Returns:
        Estimated file size in megabytes
    """
    try:
        # Get audio properties
        raw_data_size = len(audio_segment.raw_data)
        header_size = 44
        estimated_file_size_bytes = header_size + raw_data_size

        # Convert bytes to megabytes
        estimated_file_size_mb = estimated_file_size_bytes / (1024 * 1024)

        estimated_file_size_as_mp3 = (
            estimated_file_size_mb / 5
        )  # Compression rate of Mp3
        return estimated_file_size_as_mp3

    except Exception as e:
        # Fallback: use raw_data if available, otherwise estimate based on duration
        try:
            if hasattr(audio_segment, "raw_data") and audio_segment.raw_data:
                return len(audio_segment.raw_data) / (1024 * 1024)
        except:
            pass

        # Very rough estimation: assume 16-bit stereo at 44.1kHz
        duration_seconds = len(audio_segment) / 1000.0
        estimated_bytes = duration_seconds * 44100 * 2 * 2  # 44.1kHz, stereo, 16-bit
        return (estimated_bytes + 44) / (1024 * 1024)


def _get_file_size_mb(file_path: str) -> float:
    """Get file size in megabytes."""
    try:
        return os.path.getsize(file_path) / (1024 * 1024)
    except OSError as e:
        raise AudioFileError(f"Error getting file size: {e}")


def _validate_file_size(file_path: str, max_size_mb: float) -> None:
    """
    Validate that the file is within the size limit.

    Args:
        file_path: Path to the audio file
        max_size_mb: Maximum allowed size in megabytes

    Raises:
        AudioFileError: If the file is too large
    """
    try:
        size_mb = _get_file_size_mb(file_path)
        if size_mb > max_size_mb:
            raise AudioFileError(
                f"Audio file too large: {size_mb:.2f} MB exceeds limit of {max_size_mb} MB"
            )
        print(f"File size: {size_mb:.2f} MB (within {max_size_mb} MB limit)")
    except Exception as e:
        if isinstance(e, AudioFileError):
            raise
        raise AudioFileError(f"Error validating file size: {e}")


def _validate_audio_segment_size(
    audio_segment: AudioSegment, max_size_mb: float
) -> None:
    """
    Validate that the AudioSegment is within the size limit.

    Args:
        audio_segment: AudioSegment object
        max_size_mb: Maximum allowed size in megabytes

    Raises:
        AudioFileError: If the audio is too large
    """
    try:
        size_mb = _estimate_audio_segment_size_mb(audio_segment)
        if size_mb > max_size_mb:
            raise AudioFileError(
                f"AudioSegment too large: {size_mb:.2f} MB exceeds limit of {max_size_mb} MB"
            )
    except Exception as e:
        if isinstance(e, AudioFileError):
            raise
        raise AudioFileError(f"Error validating AudioSegment size: {e}")


def _validate_audio_file(audio_file_path: str) -> bytes:
    """Validate and read audio file content."""
    try:
        audio_path = Path(audio_file_path)
        if not audio_path.exists():
            raise AudioFileError(f"Audio file not found: {audio_file_path}")

        if not audio_path.is_file():
            raise AudioFileError(f"Path is not a file: {audio_file_path}")

        # Check if file is readable
        try:
            with open(audio_path, "rb") as f:
                file_content = f.read()
                if not file_content:
                    raise AudioFileError(f"Audio file is empty: {audio_file_path}")
                return file_content
        except PermissionError:
            raise AudioFileError(f"Permission denied reading file: {audio_file_path}")
        except OSError as e:
            raise AudioFileError(f"Error reading audio file: {e}")

    except Exception as e:
        if isinstance(e, (AudioFileError, AudioTranscriptionError)):
            raise
        raise AudioFileError(f"Unexpected error validating audio file: {e}")


def _extract_audio_segment_content(audio_segment: AudioSegment) -> tuple[bytes, str]:
    """
    Extract audio content and generate a cache key from AudioSegment.

    Returns:
        Tuple of (audio_content, cache_key)
    """
    try:
        buffer = io.BytesIO()
        audio_segment.export(buffer, format="wav", codec="pcm_s16le")
        buffer.seek(0)
        file_content = buffer.getvalue()

        if not file_content:
            raise AudioFileError("AudioSegment is empty or could not be exported")

        content_hash = hashlib.md5(file_content).hexdigest()
        return file_content, content_hash
    except Exception as e:
        raise AudioFileError(f"Error exporting AudioSegment to bytes: {e}")


def _get_audio_duration_from_file(audio_file_path: str) -> float:
    """Get audio duration in seconds from file path."""
    try:
        audio = AudioSegment.from_file(audio_file_path)
        return len(audio) / 1000  # pydub duration is in milliseconds
    except Exception as e:
        raise AudioFileError(f"Error getting duration from file: {e}")


def _get_audio_duration_from_segment(audio_segment: AudioSegment) -> float:
    """Get audio duration in seconds from AudioSegment."""
    return len(audio_segment) / 1000  # pydub duration is in milliseconds


def _load_cached_response(cache_filepath: str) -> Optional[Dict]:
    """Load cached response if available."""
    try:
        if os.path.isfile(cache_filepath):
            with open(cache_filepath, "r", encoding="utf-8") as f:
                cached_response = json.loads(f.read())
                return cached_response
    except (json.JSONDecodeError, IOError, OSError) as e:
        print(f"Error reading cache file: {e}")
    return None


def _save_to_cache(cache_filepath: str, response_data: Dict) -> None:
    """Save response to cache."""
    try:
        with open(cache_filepath, "w", encoding="utf-8") as f:
            json.dump(response_data, f, ensure_ascii=False, indent=2)
    except (IOError, OSError) as e:
        print(f"Error writing to cache file: {e}")


async def _make_api_request(
    url: str,
    headers: Dict,
    data: aiohttp.FormData,
    request: TranscriptionRequest,
    is_file_request: bool = True,
) -> Dict:
    """Make the actual API request with proper error handling."""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, data=data) as response_obj:
                if response_obj.status != 200:
                    try:
                        response_text = await response_obj.json()
                        error_msg = f"Azure OpenAI API error {response_obj.status}: {response_text}"
                    except (json.JSONDecodeError, aiohttp.ContentTypeError):
                        error_msg = f"Azure OpenAI API error {response_obj.status}: {await response_obj.text()}"
                    raise AudioTranscriptionError(error_msg)

                try:
                    response_data = await response_obj.json()
                except (json.JSONDecodeError, aiohttp.ContentTypeError) as e:
                    raise AudioTranscriptionError(
                        f"Invalid JSON response from API: {e}"
                    )

                if response_data.get("error") is not None:
                    error_code = response_data["error"].get("code")
                    if error_code == "429":  # RateLimit
                        print("RateLimit reached, waiting...")
                        await asyncio.sleep(30)
                        # Retry with the appropriate function
                        if is_file_request:
                            return await call_audio_transcription_from_file_async(
                                audio_file_path=request.audio_file_path,
                                model_id=request.model_id,
                                language=request.language,
                                response_format=request.response_format,
                                temperature=request.temperature,
                                use_cache=request.use_cache,
                                response_usage=request.response_usage,
                                max_file_size_mb=request.max_file_size_mb,
                            )
                        else:
                            return await call_audio_transcription_from_segment_async(
                                audio_segment=request.audio_segment,
                                model_id=request.model_id,
                                language=request.language,
                                response_format=request.response_format,
                                temperature=request.temperature,
                                use_cache=request.use_cache,
                                response_usage=request.response_usage,
                                max_file_size_mb=request.max_file_size_mb,
                            )
                    else:
                        raise AudioTranscriptionError(
                            f"Azure OpenAI API error: {response_data['error']}"
                        )

                return response_data

    except aiohttp.ClientError as e:
        raise AudioTranscriptionError(f"Network error during API request: {e}")
    except asyncio.TimeoutError:
        raise AudioTranscriptionError("Request timeout")
    except Exception as e:
        if isinstance(e, AudioTranscriptionError):
            raise
        raise AudioTranscriptionError(f"Unexpected error during API request: {e}")


async def _calculate_usage_cost_from_file(
    audio_file_path: str,
    model_id: str,
    response_data: Dict,
    response_usage: ResponseUsage,
) -> None:
    """Calculate and track usage costs for file-based transcription."""
    try:
        audio_duration_seconds = _get_audio_duration_from_file(audio_file_path)
        cost = await calculate_cost(
            model=model_id, audio_duration_seconds=audio_duration_seconds
        )

        llm_usage = LLMUsage(
            model_id=model_id,
            cost=cost,
            input_tokens=0,  # Not applicable for audio
            output_tokens=len(
                response_data.get("text", "")
            ),  # Use text length as proxy
            audio_input_duration=audio_duration_seconds,
        )

        response_usage.add_llm_usage(llm_usage)
    except Exception as e:
        print(f"Error calculating transcription cost: {e}")


async def _calculate_usage_cost_from_segment(
    audio_segment: AudioSegment,
    model_id: str,
    response_data: Dict,
    response_usage: ResponseUsage,
) -> None:
    """Calculate and track usage costs for AudioSegment-based transcription."""
    try:
        audio_duration_seconds = _get_audio_duration_from_segment(audio_segment)
        cost = await calculate_cost(
            model=model_id, audio_duration_seconds=audio_duration_seconds
        )

        llm_usage = LLMUsage(
            model_id=model_id,
            cost=cost,
            output_tokens=len(
                response_data.get("text", "")
            ),  # Use text length as proxy
            audio_input_duration=audio_duration_seconds,
        )
        # TODO, this is not correct
        response_usage.add_llm_usage(llm_usage)
    except Exception as e:
        print(f"Error calculating transcription cost: {e}")


async def call_audio_transcription_from_file_async(
    audio_file_path: str,
    model_id: str = OPENAI_TRANSCRIPTION_MODEL_ID,
    language: Optional[str] = None,
    response_format: str = "json",
    temperature: float = 0,
    use_cache: bool = True,
    response_usage: Optional[ResponseUsage] = None,
    max_file_size_mb: float = 25.0,
) -> Dict:
    """
    Asynchronous call to Azure OpenAI Audio Transcription from file path with caching.

    Args:
        audio_file_path: Path to the audio file
        model_id: Azure deployment name for Whisper
        language: Optional language code (e.g., 'ro', 'en')
        response_format: Format of the response ('json', 'text', 'srt', 'verbose_json', 'vtt')
        temperature: Sampling temperature (0 to 1)
        use_cache: Whether to use cached responses
        response_usage: Optional ResponseUsage object to track costs
        max_file_size_mb: Maximum allowed file size in megabytes (default: 25.0)

    Returns:
        Dict containing transcription results

    Raises:
        AudioFileError: If there are issues with the audio file or if file is too large
        AudioTranscriptionError: If there are issues with the transcription API
    """
    # Validate request parameters
    request = TranscriptionRequest(
        model_id=model_id,
        language=language,
        response_format=response_format,
        temperature=temperature,
        use_cache=use_cache,
        response_usage=response_usage,
        max_file_size_mb=max_file_size_mb,
    )
    # Add file path to request for retry logic
    request.audio_file_path = audio_file_path

    # Validate file size before audio_processing
    _validate_file_size(audio_file_path, max_file_size_mb)

    # Validate and read audio file
    file_content = _validate_audio_file(audio_file_path)
    content_hash = hashlib.md5(file_content).hexdigest()

    # Setup cache
    cache_key = f"{content_hash}_{model_id}_{language or 'auto'}"
    cache_filename = f"{cache_key}.json"
    cache_filepath = os.path.join(LLM_AUDIO_CACHE_DIR, cache_filename)

    # Try to load from cache
    if use_cache:
        cached_response = _load_cached_response(cache_filepath)
        if cached_response is not None:
            return cached_response

    # Prepare API request
    url = f"{AZURE_OPENAI_TRANSCRIPTION_ENDPOINT}/openai/deployments/{model_id}/audio/transcriptions?api-version={AZURE_TRANSCRIPTION_API_VERSION}"

    headers = {
        "api-key": OPENAI_TRANSCRIPTION_API_KEY,
    }

    # Prepare multipart form data
    data = aiohttp.FormData()
    data.add_field(
        "file",
        file_content,
        filename=os.path.basename(audio_file_path),
        content_type="audio/mpeg",  # Use original file's content type
    )

    data.add_field("model", model_id)
    data.add_field("temperature", str(temperature))

    if language:
        data.add_field("language", language)

    # Make API request
    response_data = await _make_api_request(
        url, headers, data, request, is_file_request=True
    )

    # Calculate usage if requested
    if response_usage is not None:
        await _calculate_usage_cost_from_file(
            audio_file_path, model_id, response_data, response_usage
        )

    # Cache the response
    _save_to_cache(cache_filepath, response_data)

    return response_data


async def call_audio_transcription_from_segment_async(
    audio_segment: AudioSegment,
    model_id: str = OPENAI_TRANSCRIPTION_MODEL_ID,
    language: Optional[str] = None,
    response_format: str = "json",
    temperature: float = 0,
    use_cache: bool = True,
    response_usage: Optional[ResponseUsage] = None,
    max_file_size_mb: float = 25.0,
) -> Dict:
    """
    Asynchronous call to Azure OpenAI Audio Transcription from AudioSegment with caching.

    Args:
        audio_segment: AudioSegment object
        model_id: Azure deployment name for Whisper
        language: Optional language code (e.g., 'ro', 'en')
        response_format: Format of the response ('json', 'text', 'srt', 'verbose_json', 'vtt')
        temperature: Sampling temperature (0 to 1)
        use_cache: Whether to use cached responses
        response_usage: Optional ResponseUsage object to track costs
        max_file_size_mb: Maximum allowed file size in megabytes (default: 25.0)

    Returns:
        Dict containing transcription results

    Raises:
        AudioFileError: If there are issues with the AudioSegment or if it's too large
        AudioTranscriptionError: If there are issues with the transcription API
    """
    # Validate request parameters
    request = TranscriptionRequest(
        model_id=model_id,
        language=language,
        response_format=response_format,
        temperature=temperature,
        use_cache=use_cache,
        response_usage=response_usage,
        max_file_size_mb=max_file_size_mb,
    )
    # Add audio segment to request for retry logic
    request.audio_segment = audio_segment

    # Validate AudioSegment size before audio_processing
    _validate_audio_segment_size(audio_segment, max_file_size_mb)

    # Extract audio content and generate cache key
    file_content, content_hash = _extract_audio_segment_content(audio_segment)

    # Setup cache
    cache_key = f"{content_hash}_{model_id}_{language or 'auto'}"
    cache_filename = f"{cache_key}.json"
    cache_filepath = os.path.join(LLM_AUDIO_CACHE_DIR, cache_filename)

    # Try to load from cache
    if use_cache:
        cached_response = _load_cached_response(cache_filepath)
        if cached_response is not None:
            return cached_response

    # Prepare API request
    url = f"{AZURE_OPENAI_TRANSCRIPTION_ENDPOINT}/openai/deployments/{model_id}/audio/transcriptions?api-version={AZURE_TRANSCRIPTION_API_VERSION}"

    headers = {
        "api-key": OPENAI_TRANSCRIPTION_API_KEY,
    }

    # Prepare multipart form data
    data = aiohttp.FormData()
    data.add_field(
        "file",
        file_content,
        filename="audio_segment.wav",  # Default filename for AudioSegment
        content_type="audio/wav",  # Always WAV since we export AudioSegment as WAV
    )

    data.add_field("model", model_id)
    data.add_field("temperature", str(temperature))

    if language:
        data.add_field("language", language)

    # Make API request
    response_data = await _make_api_request(
        url, headers, data, request, is_file_request=False
    )

    # Calculate usage if requested
    if response_usage is not None:
        await _calculate_usage_cost_from_segment(
            audio_segment, model_id, response_data, response_usage
        )

    # Cache the response
    _save_to_cache(cache_filepath, response_data)

    return response_data


async def transcribe_audio_file(
    audio_file_path: str,
    model_id: str = OPENAI_TRANSCRIPTION_MODEL_ID,
    language: Optional[str] = None,
    use_cache: bool = True,
    response_usage: Optional[ResponseUsage] = None,
    max_file_size_mb: float = OPENAI_MAX_AUDIO_SIZE,
) -> str:
    """
    Simplified function that returns just the transcribed text from a file path.

    Args:
        audio_file_path: Path to the audio file
        model_id: Azure deployment name for Whisper
        language: Optional language code
        use_cache: Whether to use cached responses
        response_usage: Optional ResponseUsage object to track costs
        max_file_size_mb: Maximum allowed file size in megabytes (default: 25.0)

    Returns:
        Transcribed text as string

    Raises:
        AudioFileError: If there are issues with the audio file or if file is too large
        AudioTranscriptionError: If there are issues with the transcription API
    """
    try:
        result = await call_audio_transcription_from_file_async(
            audio_file_path=audio_file_path,
            model_id=model_id,
            language=language,
            use_cache=use_cache,
            response_usage=response_usage,
            max_file_size_mb=max_file_size_mb,
        )
        return result.get("text", "")
    except (AudioFileError, AudioTranscriptionError):
        raise
    except Exception as e:
        raise AudioTranscriptionError(f"Unexpected error in transcribe_audio_file: {e}")


async def transcribe_audio_segment(
    audio_segment: AudioSegment,
    model_id: str = OPENAI_TRANSCRIPTION_MODEL_ID,
    language: Optional[str] = None,
    use_cache: bool = True,
    response_usage: Optional[ResponseUsage] = None,
    max_file_size_mb: float = OPENAI_MAX_AUDIO_SIZE,
) -> str:
    """
    Simplified function that returns just the transcribed text from an AudioSegment.

    Args:
        audio_segment: AudioSegment object
        model_id: Azure deployment name for Whisper
        language: Optional language code
        use_cache: Whether to use cached responses
        response_usage: Optional ResponseUsage object to track costs
        max_file_size_mb: Maximum allowed file size in megabytes (default: 25.0)

    Returns:
        Transcribed text as string

    Raises:
        AudioFileError: If there are issues with the AudioSegment or if it's too large
        AudioTranscriptionError: If there are issues with the transcription API
    """
    try:
        result = await call_audio_transcription_from_segment_async(
            audio_segment=audio_segment,
            model_id=model_id,
            language=language,
            use_cache=use_cache,
            response_usage=response_usage,
            max_file_size_mb=max_file_size_mb,
        )
        return result.get("text", "")
    except (AudioFileError, AudioTranscriptionError):
        raise
    except Exception as e:
        raise AudioTranscriptionError(
            f"Unexpected error in transcribe_audio_segment: {e}"
        )


def test():
    response_usage = ResponseUsage()

    # Test with file path
    sample_file = ".data/samples/recording-2.mp3"
    print(f"Testing transcription service with file: {sample_file}")
    try:
        result_file = asyncio.run(
            transcribe_audio_file(
                audio_file_path=sample_file,
                use_cache=False,
                response_usage=response_usage,
                max_file_size_mb=25.0,
            )
        )
        print("Transcription result (file):")
        print(result_file)
    except (AudioFileError, AudioTranscriptionError) as e:
        print(f"Error during file transcription: {e}")

    # Test with AudioSegment object
    print("\nTesting transcription service with AudioSegment object...")
    try:
        # Load the same file as AudioSegment
        audio_segment = AudioSegment.from_file(sample_file)
        print(f"Loaded AudioSegment: {len(audio_segment)}ms duration")
        print(
            f"AudioSegment properties: {audio_segment.frame_rate}Hz, {audio_segment.channels} channels, {audio_segment.sample_width * 8} bits"
        )

        result_segment = asyncio.run(
            transcribe_audio_segment(
                audio_segment=audio_segment,
                use_cache=False,
                response_usage=response_usage,
                max_file_size_mb=25.0,
            )
        )
        print("Transcription result (AudioSegment):")
        print(result_segment)
    except (AudioFileError, AudioTranscriptionError) as e:
        print(f"Error during AudioSegment transcription: {e}")

    print(f"\nTotal usage: {response_usage}")


if __name__ == "__main__":
    test()
