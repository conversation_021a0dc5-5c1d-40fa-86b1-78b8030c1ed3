import asyncio
import hashlib
import json
import logging
import os
import re
from typing import Dict, List, Optional, Union

import aiohttp
from lazarus_ai import <PERSON><PERSON><PERSON>

from acva_ai._params import CACHE_DIR, LAZARUS_AUTHKEY, LAZARUS_ORGID, LAZARUS_MODEL_ID
from acva_ai.utils.usage import LLMUsage, ResponseUsage

logger = logging.getLogger(__name__)

# Set up cache directory
LAZARUS_CACHE_DIR = os.path.join(CACHE_DIR, "lazarus")
os.makedirs(LAZARUS_CACHE_DIR, exist_ok=True)


def _generate_lazarus_cache_filename(
    prompt: Union[str, List[str]], model_id: str
) -> str:
    """
    Generate a stable filename for the given prompt parameters.

    Args:
        prompt: The prompt or list of prompts
        model_id: The Lazarus model ID

    Returns:
        Path to the cache file
    """
    # Convert list to string if needed
    prompt_str = json.dumps(prompt) if isinstance(prompt, list) else prompt

    # Create a unique hash based on the prompt and model
    prompt_hash = hashlib.md5(prompt_str.encode("utf-8")).hexdigest()
    cache_filename = f"{model_id}_{prompt_hash}.json"
    return os.path.join(LAZARUS_CACHE_DIR, cache_filename)


class LazarusProvider:
    """Lazarus AI LLM provider class."""

    def __init__(self):
        """Initialize the Lazarus provider."""
        self.model_id = LAZARUS_MODEL_ID

    async def call_llm(
        self,
        prompt: str,
        max_tokens: int = 5000,
        use_cache: bool = False,
        response_usage: Optional[ResponseUsage] = None,
        current_retry: int = 0,
        max_retries: int = 3,
        retry_delay: float = 10,
        temperature: float = 0,
        custom_data: Optional[Dict] = None,
        webhook_url: Optional[str] = None,
    ) -> str:
        """
        Asynchronous call to Lazarus AI API with caching.

        Args:
            prompt: The prompt or list of prompts to send to the model
            model_id: The Lazarus model ID (default: "riky2")
            max_tokens: Maximum tokens to generate (for consistency, not used by Lazarus)
            use_cache: Whether to use cached responses
            response_usage: Optional ResponseUsage object to track costs
            current_retry: Current retry attempt (used internally)
            max_retries: Maximum number of retries for errors
            retry_delay: Initial delay before retrying (will increase exponentially)
            temperature: Temperature parameter (for consistency, not used by Lazarus)
            custom_data: Optional custom JSON to include in the response
            webhook_url: Optional webhook URL for asynchronous processing

        Returns:
            The model's response as a string
        """
        task_id = None
        # Try to extract task ID from the current context if it's in the prompt
        if isinstance(prompt, str):
            task_id_match = re.search(r"\[Task ([a-f0-9-]+)\]", prompt)
            if task_id_match:
                task_id = task_id_match.group(1)

        log_prefix = f"[Task {task_id}] " if task_id else ""

        # Check cache first
        cache_filepath = _generate_lazarus_cache_filename(prompt, self.model_id)
        if os.path.isfile(cache_filepath) and use_cache:
            try:
                with open(cache_filepath, "r", encoding="utf-8") as f:
                    cached_response = json.load(f)
                logger.info(f"{log_prefix}Loaded Lazarus response from cache.")
                return cached_response["data"][0]["answer"]
            except Exception as e:
                logger.warning(f"{log_prefix}Error reading cache file: {e}")

        # Initialize Lazarus authentication
        auth = LazarusAuth(LAZARUS_ORGID, LAZARUS_AUTHKEY)

        # Use the chat endpoint directly since the lazarus-ai package doesn't support chat
        url = f"https://api.lazarusai.com/api/rikai/chat/{self.model_id}"
        headers = auth.headers  # Use the headers from the auth object

        # Prepare the data payload
        data = {"question": prompt if isinstance(prompt, list) else [prompt]}

        # Add webhook if provided
        if webhook_url:
            data["webhook"] = webhook_url

        # Add custom data if provided
        if custom_data:
            data["custom"] = custom_data

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=data) as response:
                    if response.status == 200:
                        response_data = await response.json()

                        # Track usage if provided
                        if response_usage is not None:
                            # Lazarus doesn't provide token counts, so we estimate
                            input_tokens = (
                                len(str(prompt).split()) * 1.3
                            )  # Rough estimate
                            output_tokens = len(str(response_data).split()) * 1.3

                            # Calculate cost based on model pricing
                            from acva_ai._params import LAZARUS_PRICING

                            cost = 0
                            model_pricing_dict = LAZARUS_PRICING.get(self.model_id)
                            if model_pricing_dict is not None:
                                input_cost = (
                                    model_pricing_dict["input"] * input_tokens * 10e-6
                                )
                                output_cost = (
                                    model_pricing_dict["output"] * output_tokens * 10e-6
                                )
                                cost = input_cost + output_cost

                            llm_usage = LLMUsage(
                                model_id=self.model_id,
                                cost=cost,
                                input_tokens=int(input_tokens),
                                output_tokens=int(output_tokens),
                            )

                            response_usage.add_llm_usage(llm_usage)

                        # Cache the response
                        try:
                            with open(cache_filepath, "w", encoding="utf-8") as f:
                                json.dump(
                                    response_data, f, ensure_ascii=False, indent=2
                                )
                        except Exception as e:
                            logger.warning(
                                f"{log_prefix}Error writing to cache file: {e}"
                            )

                        return response_data["data"][0]["answer"]
                    else:
                        error_text = await response.text()
                        raise Exception(
                            f"Lazarus API error {response.status}: {error_text}"
                        )

        except Exception as e:
            # Handle retries
            if current_retry < max_retries:
                wait_time = retry_delay * (2**current_retry)
                logger.warning(
                    f"{log_prefix}Lazarus API error: {str(e)}. Waiting {wait_time} seconds..."
                )
                await asyncio.sleep(wait_time)
                return await self.call_llm(
                    prompt=prompt,
                    max_tokens=max_tokens,
                    use_cache=use_cache,
                    response_usage=response_usage,
                    current_retry=current_retry + 1,
                    max_retries=max_retries,
                    retry_delay=retry_delay,
                    temperature=temperature,
                    custom_data=custom_data,
                    webhook_url=webhook_url,
                )

            # If we've exhausted retries
            logger.error(
                f"{log_prefix}Error maintained after {max_retries} retries: {str(e)}"
            )
            raise Exception(f"Lazarus API error: {str(e)}")


def test():
    """Test the Lazarus API call function."""
    response_usage = ResponseUsage()

    lazarus = LazarusProvider()

    try:
        # Test with a simple prompt
        result = asyncio.run(
            lazarus.call_llm(
                prompt="What is the capital of France?",
                response_usage=response_usage,
                use_cache=False,  # Disable caching
            )
        )
        print("API Response:")
        print(result)
        print(f"Usage: {response_usage}")

        # Test with a list of prompts
        result_list = asyncio.run(
            lazarus.call_llm(
                prompt=[
                    "What is the capital of France?",
                    "What is the capital of Italy?",
                ],
                response_usage=response_usage,
                use_cache=True,  # Enable caching
            )
        )
        print("\nList Prompt Response:")
        print(result_list)
        print(f"Usage: {response_usage}")

    except Exception as e:
        print(f"Error during test: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    test()
