import asyncio
import hashlib
import logging
import os
import re
from typing import List, Optional, Union

import numpy as np
from openai import AsyncAzureOpenAI

from acva_ai._params import (
    AZURE_API_VERSION,
    AZURE_OPENAI_API_KEY,
    AZURE_OPENAI_ENDPOINT,
    CACHE_DIR,
    OPENAI_MODEL_ID,
    OPENAI_PRICING,
)
from acva_ai.llm.llm_cache import _generate_llm_cache_filename
from acva_ai.llm.rate_limiter import AzureOpenAIRateLimiter, RateLimitConfig
from acva_ai.utils.usage import LLMUsage, ResponseUsage

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

LLM_CALLS_CACHE_DIR = os.path.join(CACHE_DIR, "openai")
EMBEDDING_CACHE_DIR = os.path.join(CACHE_DIR, "embeddings")
os.makedirs(LLM_CALLS_CACHE_DIR, exist_ok=True)
os.makedirs(EMBEDDING_CACHE_DIR, exist_ok=True)

# Initialize Azure OpenAI client
azure_client = AsyncAzureOpenAI(
    api_key=AZURE_OPENAI_API_KEY,
    api_version=AZURE_API_VERSION,
    azure_endpoint=AZURE_OPENAI_ENDPOINT,
)

# Initialize the rate limiter with Azure OpenAI limits
rate_limiter = AzureOpenAIRateLimiter(
    {
        "gpt-4o": RateLimitConfig(
            tokens_per_minute=250000,  # 250k tokens per minute
            requests_per_minute=1500,  # 1500 requests per minute
        ),
        # Add other models as needed with their specific limits
    }
)


def estimate_tokens(text: str) -> int:
    """
    Estimate the number of tokens in a text string.
    This is a rough estimate: ~4 characters per token for English text.

    Args:
        text: The text to estimate tokens for

    Returns:
        Estimated token count
    """
    return len(text) // 4 + 1


class AzureProvider:
    """Azure OpenAI LLM provider class."""

    def __init__(self):
        """Initialize the Azure provider."""
        self.model_id = OPENAI_MODEL_ID

    async def call_llm(
        self,
        prompt: str,
        max_tokens: int = 5000,
        use_cache: bool = False,
        response_usage: Optional[ResponseUsage] = None,
        current_retry: int = 0,
        max_retries: int = 3,
        retry_delay: float = 10,
        temperature: float = 0,
    ) -> str:
        """
        Asynchronous call to Azure OpenAI Chat Completion with caching and rate limit handling.

        Args:
            prompt: The prompt to send to the model
            model_id: The model ID (Azure deployment name)
            max_tokens: Maximum tokens to generate
            use_cache: Whether to use cached responses
            response_usage: Optional ResponseUsage object to track costs
            current_retry: Current retry attempt (used internally)
            max_retries: Maximum number of retries for rate limit errors
            retry_delay: Initial delay before retrying (will increase exponentially)
            temperature: Temperature parameter for the model

        Returns:
            The model's response as a string
        """
        task_id = None
        # Try to extract task ID from the current context if it's in the prompt
        task_id_match = re.search(r"\[Task ([a-f0-9-]+)\]", prompt)
        if task_id_match:
            task_id = task_id_match.group(1)

        log_prefix = f"[Task {task_id}] " if task_id else ""

        # Check cache first
        cache_filepath = _generate_llm_cache_filename(prompt, self.model_id, max_tokens)
        if os.path.isfile(cache_filepath) and use_cache:
            try:
                with open(cache_filepath, "r", encoding="utf-8") as f:
                    cached_response = f.read()
                logger.info(f"{log_prefix}Loaded async LLM response from cache.")
                return cached_response
            except Exception as e:
                logger.warning(f"{log_prefix}Error reading cache file: {e}")

        try:
            # Use Azure OpenAI client instead of direct URL calls
            response = await azure_client.chat.completions.create(
                model=self.model_id,
                messages=[{"role": "system", "content": prompt}],
                max_tokens=max_tokens,
                temperature=temperature,
            )

            # Convert response to dict format for compatibility with existing code
            response_data = {
                "choices": [
                    {
                        "message": {"content": response.choices[0].message.content},
                        "finish_reason": response.choices[0].finish_reason,
                    }
                ],
                "usage": (
                    {
                        "prompt_tokens": (
                            response.usage.prompt_tokens if response.usage else 0
                        ),
                        "completion_tokens": (
                            response.usage.completion_tokens if response.usage else 0
                        ),
                        "total_tokens": (
                            response.usage.total_tokens if response.usage else 0
                        ),
                    }
                    if response.usage
                    else None
                ),
            }

            # Extract the response text
            response_msg = response_data["choices"][0]["message"]["content"]

            # Track usage if provided
            if response_usage is not None and response_data.get("usage"):
                usage_data = response_data["usage"]
                input_tokens = usage_data["prompt_tokens"]
                output_tokens = usage_data["completion_tokens"]

                # Calculate cost based on model pricing
                from acva_ai._params import AZURE_PRICING

                cost = None
                model_pricing_dict = AZURE_PRICING.get(self.model_id)
                if model_pricing_dict is not None:
                    input_cost = model_pricing_dict["input"] * input_tokens * 10e-6
                    output_cost = model_pricing_dict["output"] * output_tokens * 10e-6
                    cost = input_cost + output_cost

                llm_usage = LLMUsage(
                    model_id=self.model_id,
                    cost=cost,
                    input_tokens=input_tokens,
                    output_tokens=output_tokens,
                )

                response_usage.add_llm_usage(llm_usage)

            # Cache the response
            try:
                with open(cache_filepath, "w", encoding="utf-8") as f:
                    f.write(response_msg)
            except Exception as e:
                logger.warning(f"{log_prefix}Error writing to cache file: {e}")

            return response_msg

        except Exception as e:
            from openai import RateLimitError, APIError, APIConnectionError

            # Handle rate limiting errors
            if isinstance(e, RateLimitError):
                if current_retry < max_retries:
                    wait_time = retry_delay * (2**current_retry)
                    logger.warning(
                        f"{log_prefix}Rate limit reached. Waiting {wait_time} seconds..."
                    )
                    await asyncio.sleep(wait_time)
                    return await self.call_llm(
                        prompt=prompt,
                        max_tokens=max_tokens,
                        use_cache=use_cache,
                        response_usage=response_usage,
                        current_retry=current_retry + 1,
                        max_retries=max_retries,
                        retry_delay=retry_delay,
                        temperature=temperature,
                    )

            # Handle API errors (server errors, etc.)
            if isinstance(e, (APIError, APIConnectionError)):
                if current_retry < max_retries:
                    wait_time = retry_delay * (2**current_retry)
                    logger.warning(
                        f"{log_prefix}API error: {str(e)}. Waiting {wait_time} seconds..."
                    )
                    await asyncio.sleep(wait_time)
                    return await self.call_llm(
                        prompt=prompt,
                        max_tokens=max_tokens,
                        use_cache=use_cache,
                        response_usage=response_usage,
                        current_retry=current_retry + 1,
                        max_retries=max_retries,
                        retry_delay=retry_delay,
                        temperature=temperature,
                    )

            # If we've exhausted retries or it's another type of error
            if current_retry >= max_retries:
                logger.error(
                    f"{log_prefix}Error maintained after {max_retries} retries: {str(e)}"
                )
                raise Exception(f"Azure OpenAI API error: {str(e)}")

            # For other errors, retry once
            wait_time = retry_delay
            logger.warning(
                f"{log_prefix}Unexpected error: {str(e)}. Retrying in {wait_time} seconds..."
            )
            await asyncio.sleep(wait_time)
            return await self.call_llm(
                prompt=prompt,
                max_tokens=max_tokens,
                use_cache=use_cache,
                response_usage=response_usage,
                current_retry=current_retry + 1,
                max_retries=max_retries,
                retry_delay=retry_delay,
                temperature=temperature,
            )


def test():
    # This is how to track costs:
    response_usage = ResponseUsage()
    openai = AzureProvider()

    result = asyncio.run(
        openai.call_llm(
            prompt="What is the capital of France?",
            max_tokens=1000,
            response_usage=response_usage,
            use_cache=False,
        )
    )
    print(result)

    print(response_usage)


async def test_rate_limit(num_parallels: int = 2000, num_sequence: int = 30):
    openai = AzureProvider()
    response_usage = ResponseUsage()
    for i in range(num_sequence):
        print(f"Test {i+1} / {num_sequence}")
        tasks = []
        for _ in range(num_parallels):
            tasks.append(
                openai.call_llm(
                    prompt=f"Write me a report on the history of the roman Empire",
                    max_tokens=5000,
                    response_usage=response_usage,
                    use_cache=False,
                    temperature=0.5,
                )
            )
        await asyncio.gather(*tasks)

    print(response_usage)


if __name__ == "__main__":
    test()
