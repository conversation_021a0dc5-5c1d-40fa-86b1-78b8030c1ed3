import asyncio
import hashlib
import json
import logging
import os
import re
from typing import Dict, List, Optional, Union

from openai import Async<PERSON>penA<PERSON>

from acva_ai._params import CACHE_DIR, OPENAI_API, OPENAI_MODEL_ID
from acva_ai.utils.usage import LLMUsage, ResponseUsage

logger = logging.getLogger(__name__)

# Set up cache directory
OPENAI_CACHE_DIR = os.path.join(CACHE_DIR, "openai")
os.makedirs(OPENAI_CACHE_DIR, exist_ok=True)


def _generate_openai_cache_filename(
    prompt: Union[str, List[Dict]], model_id: str, max_tokens: int
) -> str:
    """
    Generate a stable filename for the given prompt parameters.

    Args:
        prompt: The prompt or list of messages
        model_id: The OpenAI model ID
        max_tokens: Maximum tokens to generate

    Returns:
        Path to the cache file
    """
    # Convert to string for hashing
    prompt_str = json.dumps(prompt) if isinstance(prompt, list) else prompt

    # Create a unique hash based on the prompt, model, and max_tokens
    hash_input = f"{prompt_str}_{model_id}_{max_tokens}"
    prompt_hash = hashlib.md5(hash_input.encode("utf-8")).hexdigest()
    cache_filename = f"{model_id}_{prompt_hash}.json"
    return os.path.join(OPENAI_CACHE_DIR, cache_filename)


class OpenAIProvider:
    """OpenAI LLM provider class."""

    def __init__(self):
        """Initialize the OpenAI provider."""
        self.client = AsyncOpenAI(api_key=OPENAI_API)
        self.model_id = OPENAI_MODEL_ID

    async def call_llm(
        self,
        prompt: str,
        max_tokens: int = 5000,
        use_cache: bool = False,
        response_usage: Optional[ResponseUsage] = None,
        current_retry: int = 0,
        max_retries: int = 3,
        retry_delay: float = 10,
        temperature: float = 0,
    ) -> str:
        """
        Asynchronous call to OpenAI API with caching and retry logic.

        Args:
            prompt: The prompt (string) or messages (list of dicts with role and content)
            max_tokens: Maximum tokens to generate
            use_cache: Whether to use cached responses
            response_usage: Optional ResponseUsage object to track costs
            current_retry: Current retry attempt (used internally)
            max_retries: Maximum number of retries for rate limit errors
            retry_delay: Initial delay before retrying (will increase exponentially)
            temperature: Temperature parameter for the model

        Returns:
            The model's response as a string
        """
        task_id = None
        # Try to extract task ID from the current context if it's in the prompt
        if isinstance(prompt, str):
            task_id_match = re.search(r"\[Task ([a-f0-9-]+)\]", prompt)
            if task_id_match:
                task_id = task_id_match.group(1)

        log_prefix = f"[Task {task_id}] " if task_id else ""

        # Check cache first
        cache_filepath = _generate_openai_cache_filename(
            prompt, self.model_id, max_tokens
        )
        if os.path.isfile(cache_filepath) and use_cache:
            try:
                with open(cache_filepath, "r", encoding="utf-8") as f:
                    cached_data = json.load(f)
                    cached_response = cached_data.get("response", "")
                logger.info(f"{log_prefix}Loaded OpenAI response from cache.")
                return cached_response
            except Exception as e:
                logger.warning(f"{log_prefix}Error reading cache file: {e}")

        # Prepare the messages based on prompt type
        if isinstance(prompt, str):
            messages = [{"role": "system", "content": prompt}]
        else:
            messages = prompt

        try:
            # Make the API call using the OpenAI client
            response = await self.client.chat.completions.create(
                model=self.model_id,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
            )

            # Extract the response text
            response_msg = response.choices[0].message.content

            # Track usage if provided
            if response_usage is not None and response.usage:
                input_tokens = response.usage.prompt_tokens
                output_tokens = response.usage.completion_tokens

                # Calculate cost based on model pricing
                from acva_ai._params import OPENAI_PRICING

                cost = None
                model_pricing_dict = OPENAI_PRICING.get(self.model_id)
                if model_pricing_dict is not None:
                    input_cost = model_pricing_dict["input"] * input_tokens * 10e-6
                    output_cost = model_pricing_dict["output"] * output_tokens * 10e-6
                    cost = input_cost + output_cost

                llm_usage = LLMUsage(
                    model_id=self.model_id,
                    cost=cost,
                    input_tokens=input_tokens,
                    output_tokens=output_tokens,
                )

                response_usage.add_llm_usage(llm_usage)

            # Cache the response
            try:
                cache_data = {
                    "response": response_msg,
                    "usage": {
                        "prompt_tokens": (
                            response.usage.prompt_tokens if response.usage else 0
                        ),
                        "completion_tokens": (
                            response.usage.completion_tokens if response.usage else 0
                        ),
                        "total_tokens": (
                            response.usage.total_tokens if response.usage else 0
                        ),
                    },
                }
                with open(cache_filepath, "w", encoding="utf-8") as f:
                    json.dump(cache_data, f, ensure_ascii=False, indent=2)
            except Exception as e:
                logger.warning(f"{log_prefix}Error writing to cache file: {e}")

            return response_msg

        except Exception as e:
            from openai import RateLimitError, APIError, APIConnectionError

            # Handle rate limiting errors
            if isinstance(e, RateLimitError):
                if current_retry < max_retries:
                    wait_time = retry_delay * (2**current_retry)
                    logger.warning(
                        f"{log_prefix}Rate limit reached. Waiting {wait_time} seconds..."
                    )
                    await asyncio.sleep(wait_time)
                    return await self.call_llm(
                        prompt=prompt,
                        max_tokens=max_tokens,
                        use_cache=use_cache,
                        response_usage=response_usage,
                        current_retry=current_retry + 1,
                        max_retries=max_retries,
                        retry_delay=retry_delay,
                        temperature=temperature,
                    )

            # Handle API errors (server errors, etc.)
            if isinstance(e, (APIError, APIConnectionError)):
                if current_retry < max_retries:
                    wait_time = retry_delay * (2**current_retry)
                    logger.warning(
                        f"{log_prefix}API error: {str(e)}. Waiting {wait_time} seconds..."
                    )
                    await asyncio.sleep(wait_time)
                    return await self.call_llm(
                        prompt=prompt,
                        max_tokens=max_tokens,
                        use_cache=use_cache,
                        response_usage=response_usage,
                        current_retry=current_retry + 1,
                        max_retries=max_retries,
                        retry_delay=retry_delay,
                        temperature=temperature,
                    )

            # If we've exhausted retries or it's another type of error
            if current_retry >= max_retries:
                logger.error(
                    f"{log_prefix}Error maintained after {max_retries} retries: {str(e)}"
                )
                raise Exception(f"OpenAI API error: {str(e)}")

            # For other errors, retry once
            wait_time = retry_delay
            logger.warning(
                f"{log_prefix}Unexpected error: {str(e)}. Retrying in {wait_time} seconds..."
            )
            await asyncio.sleep(wait_time)
            return await self.call_llm(
                prompt=prompt,
                max_tokens=max_tokens,
                use_cache=use_cache,
                response_usage=response_usage,
                current_retry=current_retry + 1,
                max_retries=max_retries,
                retry_delay=retry_delay,
                temperature=temperature,
            )


def test():
    """Test the OpenAI API call function."""
    response_usage = ResponseUsage()

    openai = OpenAIProvider()

    # Test with a simple prompt
    result = asyncio.run(
        openai.call_llm(
            prompt="What is the capital of France?",
            max_tokens=1000,
            response_usage=response_usage,
            use_cache=False,
        )
    )
    print(result)
    print(response_usage)

    # Test with a message list
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "What is the capital of Italy?"},
    ]
    result_messages = asyncio.run(
        openai.call_llm(
            prompt=messages,
            max_tokens=1000,
            response_usage=response_usage,
            use_cache=False,
        )
    )
    print(result_messages)
    print(response_usage)


if __name__ == "__main__":
    test()
