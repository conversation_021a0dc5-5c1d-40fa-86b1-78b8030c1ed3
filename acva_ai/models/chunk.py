from typing import List
from uuid import UUID

from pydantic import BaseModel


class Word(BaseModel):
    task_id: UUID
    word: str
    start: int
    end: int
    confidence: float


class Chunk(BaseModel):
    audio_id: UUID
    task_id: UUID
    raw_words_list: List[Word]
    chunk_transcript: str
    start: int
    end: int
    min_confidence: float
    max_confidence: float
    avg_confidence: float
    observations: str
    curated_chunk_transcript: str


class CorrectedChunk(BaseModel):
    observations: str
    curated_chunk_transcript: str
