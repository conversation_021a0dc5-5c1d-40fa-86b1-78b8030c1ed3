from pydantic import BaseModel, model_validator
from typing import Literal, List
from uuid import UUID


class Task(BaseModel):
    task_id: UUID
    status: Literal["audio_processing", "done", "queued"] = "audio_processing"


class Audio(BaseModel):
    stream_id: UUID
    task_id: UUID
    input_path: str
    output_path: str


class Prompt(BaseModel):
    name: str
    prompt: str
    description: str


class Symptoms(BaseModel):
    symptoms: List[str]

    @model_validator(mode="after")
    def extract_from_nested(cls, values):
        if "symptoms" in values:
            return values
        if "data" in values and "symptoms" in values["data"]:
            return {"symptoms": values["data"]["symptoms"]}
        # Attempt deeper extraction if needed
        for val in values.values():
            if isinstance(val, dict) and "symptoms" in val:
                return {"symptoms": val["symptoms"]}
        raise ValueError("Unable to find symptoms field in response")


class Medications(BaseModel):
    medications: List[str]


class Result(BaseModel):
    task_id: UUID
    transcript: str
    simptome: List[str]
    medicamente: dict[str, List]
    observatii: List[dict]


class LLMBool(BaseModel):
    response: bool


class GeneralReponse(BaseModel):
    response: List[str]


class MedicalReport(BaseModel):
    task_id: UUID
    medical_report: dict
    transcript: str
    observatii: List[dict]
    speaker_transcript: List[dict]
