from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from uuid import UUID
from acva_ai.models.medication import Medication
from acva_ai.models.affection import Affection
from acva_ai.models.domain_insight import DomainInsight
from acva_ai.utils.usage import ResponseUsage


class VisitReport(BaseModel):
    """
    The object that contains all the processed data corresponding to a Visit.
    Status tracking is handled separately by ProcessingStatus.
    """

    # Identification
    task_id: str
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = None

    # Core Results
    raw_transcript: Optional[str] = None
    transcript: Optional[str] = None
    transcript_observations: List[str] = Field(default_factory=list)
    grammar_observations: List[str] = Field(default_factory=list)
    medical_report: Dict[str, Any] = Field(default_factory=dict)

    # Extracted Information
    medications: List[Medication] = Field(default_factory=list)
    affections: List[Affection] = Field(default_factory=list)
    domain_insights: List[DomainInsight] = Field(default_factory=list)

    # Generated Content
    html_note: Optional[str] = None

    # Usage Tracking
    response_usage: Optional[ResponseUsage] = None
    metadata: Optional[Dict[str, Any]] = None

    class Config:
        json_encoders = {datetime: lambda dt: dt.isoformat() if dt else None}

    def update_timestamps(self) -> None:
        """Update the timestamps when the report is modified"""
        self.updated_at = datetime.utcnow()

    def dict(self, *args, **kwargs):
        """Override dict method to ensure datetime serialization"""
        d = super().dict(*args, **kwargs)
        # Convert datetime objects to ISO format strings
        if d.get("created_at"):
            d["created_at"] = d["created_at"].isoformat()
        if d.get("updated_at"):
            d["updated_at"] = d["updated_at"].isoformat()
        return d

    class Config:
        arbitrary_types_allowed = True
