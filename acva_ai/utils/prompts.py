CORRECT_CHUNK_NAME = "correct_chunk"
CORRECT_CHUNK = """
    Esti un corector profesionist din domeniul medical care verifica validitatea unor afirmatii cu caracter medical.
    Textul contine cuvinte folosite incorect sau gramatical gresite. Identifica aceste cazuri si corecteaza cuvintele respective.
    Teeztul provine dintr-un speech-to-text API, te poti gandi si la greseeli caree vin din calitatea mai slaba a audioului.
    Nu reintrepta sau deduce sensul textului. Doar corecteaza cuvintele gresite si/sau umple cuvintele lipsa.
    Corecteaza textul urmator:\n\n'''{}'''\n\n. 
    Motiveaza corectia facuta printr-o propozitie.
    Returneaza rezultatele in format json, urmand aceasta schema: {}"""

GET_SYMPTOMS_NAME = "get_symptoms"
GET_SYMPTOMS = """Esti un specialist pe simptome medicale. Rolul tau este sa extragi simptome dintr-un text dat: ```\n{}\n```.
Exemple:
```
- tuse
- frisoane
- greată
- temperatura
- gat iritat foarte rău
- dureri de oase, de mușchi
```
Extrage informatiile, urmand aceasta schema: {}
Returneaza doar o lista cu simptomele si nimic altceva.
"""

GET_MEDICATIONS_NAME = "get_medications"
GET_MEDICATIONS = """
Ti se da un text cu caracter medical. Rolul tau este sa extragi toate potentialele denumiri de medicamente intr-o lista de stringuri.
Aici este textul: ```\n{}\n```.
Exemple:
```
- Erdomet
- Picături de nas
- Dezinfectant de gât
- Vitamin A
- Azitromicina de 500
```
Extrage informatiile, urmand aceasta schema: {}
"""

GET_CONTENT_NAME = "get_content"
GET_CONTENT = """
Ti se da un text cu caracter medical. Rolul tau este sa il structurezi si sa il formatezi pentru a fi usor de inteles unui medic specialist.
Aici este textul: ```\n{}\n```

Retuneaza doar textul structurat
"""

# Informatii medicale
MOTIVUL_VIZITEI_NAME = "motivul_vizitei"
MOTIVUL_VIZITEI = """
Ai la dispozitie o conversatie medicala: ```\n\n{transcript}\n\n```.
Extrage din aceasta **Motivul vizitei** pacientului.
Prin **Motivul vizitei**, ne referim la problema de sanatate pe care o prezinta pacientul medicului.
"""
ISTORIC_MEDICAL_NAME = "istoric_medical"
ISTORIC_MEDICAL = """
Ai la dispozitie o conversatie medicala: ```\n\n{transcript}\n\n```.
Extrage din aceasta **Istoricul medical** al pacientului.
Prin "Istoric medical", ne referim la afectiuni cronice, tratamente anterioare, patologii, predispozitii genetice.  
"""
MEDICATIA_ACTUALA_NAME = "medicatia_actuala"
MEDICATIA_ACTUALA = """
Ai la dispozitie o conversatie medicala: ```\n\n{transcript}\n\n```.
Extrage din aceasta **Medicatia actuala** a pacientului.
Prin "Medicatia actuala", ne referim la medicamentele luate a priori vizitei sau medicatie recurenta (in caz de boli cronice).  
"""
ALERGII_NAME = "alergii"
ALERGII = """
Ai la dispozitie o conversatie medicala: ```\n\n{transcript}\n\n```.
Extrage din aceasta **Alergii** ale pacientului. 
"""

# Informatii despre diagnostic
DIAGNOSTIC_NAME = "diagnostic"
DIAGNOSTIC = """
Ai la dispozitie o conversatie medicala: ```\n\n{transcript}\n\n```.
Extrage din aceasta **Diagnosticul** medicului pentru pacient. 
"""

REZULTATE_TESTE_LABORATOR_NAME = "rezultate_teste_laborator"
REZULTATE_TESTE_LABORATOR = """
Ai la dispozitie o conversatie medicala: ```\n\n{transcript}\n\n```.
Extrage din aceasta orice informatii legate de **Test de laborator**, care include, dar nu sunt limitate la: analize de sange, analize hormonale, analize de scaun sau urina, etc.
"""
REZULTATE_IMAGISTICE_NAME = "rezultate_imagistice"
REZULTATE_IMAGISTICE = """
Ai la dispozitie o conversatie medicala: ```\n\n{transcript}\n\n```.
Extrage din aceasta orice informatii legate de **rezultate imagistice**, care include, dar nu sunt limitate la: ultrasonografii, RMN, Raze X, etc. 
"""

# Informatii despre tratament
PLAN_DE_TRATAMENT_NAME = "plan_de_tratament"
PLAN_DE_TRATAMENT = """
Ai la dispozitie o conversatie medicala: ```\n\n{transcript}\n\n```.
Extrage din aceasta orice informatii legate de **Plan de tratament**, aceasta incluzand, dar nelimitandu-se la: medicamentele prescrise, recomandari de urmat, etc. 
"""

PROCEDURI_NAME = "proceduri"
PROCEDURI = """"""

MODIFICARE_CU_RAG = """
Ai la dispozitie un fragment dintr-o conversatie medicala: ```\n\n{transcript}\n\n```.
Cu ajutorul dictionarului medical primit: ```\n\n{medical_dictionary}\n\n```. 
Modifica conversatia, doar daca este cazul, cu cuvintele din dictionar, doar daca se protrivesc, pentru a face conversatia mai tehnica din punct de vedere medical. Returneaza doar textul modificat si nimic altceva
"""


prompt_templates = {
    "MOTIVUL_VIZITEI": MOTIVUL_VIZITEI,
    "ISTORIC_MEDICAL": ISTORIC_MEDICAL,
    "MEDICATIA_ACTUALA": MEDICATIA_ACTUALA,
    "ALERGII": ALERGII,
    "DIAGNOSTIC": DIAGNOSTIC,
    "REZULTATE_TESTE_LABORATOR": REZULTATE_TESTE_LABORATOR,
    "REZULTATE_IMAGISTICE": REZULTATE_IMAGISTICE,
    "PLAN_DE_TRATAMENT": PLAN_DE_TRATAMENT,
}
