import logging
import os
import uuid
from typing import List

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, UploadFile
from pydub import AudioSegment

from acva_ai._params import TMP_DATA_DIR

logger = logging.getLogger(__name__)


from typing import List, Union

from fastapi import UploadFile
from pydub import AudioSegment


def concatenate_audio_files(
    uploaded_files: Union[List[UploadFile], UploadFile],
) -> AudioSegment:
    """
    Concatenate a list of audio UploadFile objects into one AudioSegment.

    Args:
        uploaded_files (Union[List[UploadFile], UploadFile]):
            One or more FastAPI UploadedFile objects representing audio files.

    Returns:
        AudioSegment: The concatenated audio.

    Raises:
        ValueError: If no valid audio files are provided.
        Exception: If file format is unsupported or decoding fails.
    """
    if isinstance(uploaded_files, UploadFile):
        uploaded_files = [uploaded_files]

    if not uploaded_files:
        raise ValueError("No audio files provided.")

    supported_mime_types = {
        "audio/wav",
        "audio/x-wav",
        "audio/mpeg",
        "audio/mp3",
        "audio/x-m4a",
        "audio/aac",
        "audio/ogg",
        "audio/flac",
        "audio/webm",
        "video/webm",
    }

    combined_audio = None

    for file in uploaded_files:
        if file.content_type not in supported_mime_types:
            raise ValueError(f"Unsupported audio type: {file.content_type}")

        # Try loading based on mime type or file extension
        try:
            audio = AudioSegment.from_file(file.file)
        except Exception as e:
            raise Exception(f"Could not decode file {file.filename}: {e}")

        if combined_audio is None:
            combined_audio = audio
        else:
            combined_audio += audio

    if combined_audio is None:
        raise ValueError("No valid audio segments could be combined.")

    return combined_audio


# Add WebM, M4A, and OGG to supported formats
SUPPORTED_FORMATS = {"mp3", "wav", "m4a", "ogg", "flac", "webm", "aac"}


async def concatenate_and_convert_audio(files: List[UploadFile]) -> AudioSegment:
    """
    Concatenate multiple audio files into a single AudioSegment.

    Args:
        files: List of uploaded audio files (typically WebM, M4A, and OGG from browser recordings)

    Returns:
        AudioSegment: Combined audio as a normalized AudioSegment
    """
    if not files:
        raise HTTPException(status_code=400, detail="No audio files provided")

    temp_files = []

    try:
        segments = []

        # Process files sequentially to avoid file handle issues
        for upload_file in files:
            logger.info(
                f"Processing file: {upload_file.filename} with content type: {upload_file.content_type}"
            )

            # Try to determine format from content type if available
            content_type = upload_file.content_type or ""
            format_from_content = None

            if "wav" in content_type:
                format_from_content = "wav"
            elif "mp3" in content_type or "mpeg" in content_type:
                format_from_content = "mp3"
            elif "aac" in content_type:
                format_from_content = "aac"
            elif "ogg" in content_type:
                format_from_content = "ogg"
            elif "webm" in content_type:
                format_from_content = "webm"
            elif "flac" in content_type:
                format_from_content = "flac"
            elif "m4a" in content_type:
                format_from_content = "m4a"

            # Extract file extension as fallback
            file_ext = (
                upload_file.filename.split(".")[-1].lower()
                if "." in upload_file.filename
                else ""
            )

            # Use content type format if available, otherwise use extension
            format_to_use = format_from_content or file_ext

            logger.info(
                f"Determined format: {format_to_use} for file {upload_file.filename}"
            )

            # Skip validation to try processing all files
            # Create unique filename to avoid conflicts
            unique_id = str(uuid.uuid4())
            temp_filename = f"{unique_id}_{upload_file.filename}"
            file_path = os.path.join(TMP_DATA_DIR, temp_filename)
            temp_files.append(file_path)

            try:
                # Read content and save to temporary file
                content = await upload_file.read()

                if not content:
                    logger.warning(f"Empty content for file {upload_file.filename}")
                    continue

                # Write to temporary file
                with open(file_path, "wb") as buffer:
                    buffer.write(content)

                # Reset file pointer for potential future use
                await upload_file.seek(0)

                # Try to load audio segment with format hint if available
                try:
                    if format_to_use:
                        segment = AudioSegment.from_file(
                            file_path, format=format_to_use
                        )
                    else:
                        segment = AudioSegment.from_file(file_path)

                    segments.append(segment)
                    logger.info(
                        f"Successfully loaded audio from {upload_file.filename}"
                    )
                except Exception as format_error:
                    # If format-specific loading fails, try without format
                    logger.warning(
                        f"Failed to load with format {format_to_use}: {format_error}"
                    )
                    segment = AudioSegment.from_file(file_path)
                    segments.append(segment)
                    logger.info(
                        f"Successfully loaded audio from {upload_file.filename} without format hint"
                    )

            except Exception as e:
                logger.error(f"Error processing file {upload_file.filename}: {str(e)}")
                continue

        if not segments:
            logger.error(
                "No valid audio segments could be processed from the provided files"
            )
            raise HTTPException(
                status_code=400, detail="No valid audio files were processed"
            )

        # Combine segments sequentially
        combined = segments[0]
        for segment in segments[1:]:
            combined += segment

        return combined

    finally:
        # Clean up temp files
        for temp_file in temp_files:
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except Exception as cleanup_error:
                    logger.warning(
                        f"Failed to remove temporary file {temp_file}: {cleanup_error}"
                    )


def save_uploaded_file(file, extension) -> tuple[str, str, str]:
    """Saves uploaded file and returns file paths."""
    task_id = str(uuid.uuid4())
    file_path = os.path.join(TMP_DATA_DIR, f"{task_id}.{extension}")
    wav_file_path = os.path.join(TMP_DATA_DIR, f"{task_id}.wav")

    with open(file_path, "wb") as buffer:
        buffer.write(file.file.read())

    return task_id, file_path, wav_file_path


def convert_to_wav(input_path: str, output_path: str) -> None:
    """
    Converts an audio file (including WebM) to WAV format.

    Args:
        input_path: Path to input audio file
        output_path: Path to save the WAV file
    """
    try:
        # For WebM files, use specific parameters
        if input_path.lower().endswith(".webm"):
            # Load the WebM file - pydub uses ffmpeg under the hood
            audio = AudioSegment.from_file(input_path, format="webm")
        else:
            # For other formats
            audio = AudioSegment.from_file(input_path)

        # Export as WAV with PCM encoding (standard for WAV)
        audio.export(output_path, format="wav", codec="pcm_s16le")
        logger.info(f"Audio converted to WAV: {output_path}")

    except Exception as e:
        logger.error(f"Failed to convert audio to WAV: {str(e)}")
        raise HTTPException(
            status_code=400, detail=f"Failed to convert audio file: {str(e)}"
        )


def save_and_convert_to_wav(file, extension) -> tuple[str, str, list[str]]:
    """Saves the uploaded file, converts it to WAV with chunking, and returns file paths."""
    task_id = str(uuid.uuid4())
    file_path = os.path.join(TMP_DATA_DIR, f"{task_id}.{extension}")
    wav_file_paths = []

    with open(file_path, "wb") as buffer:
        buffer.write(file.file.read())

    try:
        audio = AudioSegment.from_file(file_path)
        chunk_length_ms = 2 * 60 * 1000  # 2 minutes per chunk
        chunks = [
            audio[i : i + chunk_length_ms]
            for i in range(0, len(audio), chunk_length_ms)
        ]

        for i, chunk in enumerate(chunks):
            chunk_path = os.path.join(TMP_DATA_DIR, f"{task_id}_chunk_{i}.wav")
            chunk.export(chunk_path, format="wav", codec="pcm_s16le")
            wav_file_paths.append(chunk_path)
            logger.info(f"Exported chunk {i} to {chunk_path}")

    except Exception as e:
        logger.error(f"Error converting file to WAV: {e}")
        raise

    return task_id, file_path, wav_file_paths


def normalize_audio_segment(
    audio_segment: AudioSegment, no_channels: int = 1, max_sample_rate: int = 48_000
) -> AudioSegment:
    audio_segment = audio_segment.set_channels(1)
    if audio_segment.frame_rate > 48000:
        audio_segment = audio_segment.set_frame_rate(48000)
    return audio_segment
