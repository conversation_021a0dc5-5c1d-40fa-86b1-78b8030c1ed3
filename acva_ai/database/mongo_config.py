from typing import Any, ClassVar, Dict, List, Optional, TypedDict

from pydantic_settings import BaseSettings


class IndexSpec(TypedDict):
    field: str
    direction: int  # 1 for ascending, -1 for descending


class CompoundIndex(TypedDict):
    fields: List[IndexSpec]
    unique: Optional[bool] = False


class MongoDBIndexingPolicy(TypedDict):
    processing_status_indexes: List[IndexSpec]
    visit_reports_indexes: List[IndexSpec]
    compound_indexes: Optional[List[CompoundIndex]] = None


class MongoSettings(BaseSettings):
    """
    Configuration settings for MongoDB
    """

    MONGO_DB_CONNECTION_STRING: str
    DATABASE_NAME: str = "acva-medical"

    # Collection names
    PROCESSING_STATUS_COLLECTION: str = "audio_processing-status"
    VISIT_REPORTS_COLLECTION: str = "visit-reports"

    # Performance settings
    CONNECTION_TIMEOUT_MS: int = 30000  # 30 seconds
    SERVER_SELECTION_TIMEOUT_MS: int = 5000  # 5 seconds
    MAX_POOL_SIZE: int = 100
    MIN_POOL_SIZE: int = 0
    MAX_IDLE_TIME_MS: int = 30000  # 30 seconds

    # Indexing policies as class variables (not model fields)
    PROCESSING_STATUS_INDEXING_POLICY: ClassVar[MongoDBIndexingPolicy] = {
        "processing_status_indexes": [
            {"field": "task_id", "direction": 1},  # Unique index
            {"field": "overall_status", "direction": 1},  # For filtering
            {"field": "start_time", "direction": -1},  # For sorting
        ],
        "compound_indexes": [
            {
                "fields": [
                    {"field": "overall_status", "direction": 1},
                    {"field": "start_time", "direction": -1}
                ],
                "unique": False
            }
        ]
    }

    VISIT_REPORTS_INDEXING_POLICY: ClassVar[MongoDBIndexingPolicy] = {
        "processing_status_indexes": [
            {"field": "task_id", "direction": 1},  # Unique index
            {"field": "created_at", "direction": -1},  # For sorting
        ],
        "compound_indexes": []
    }

    class Config:
        env_prefix = ""  # Remove prefix since we want to match exact env var name 