import logging
from acva_ai.database.mongo import mongo_instance

logger = logging.getLogger(__name__)


async def initialize_database():
    """
    Initialize both Cosmos DB and MongoDB databases and containers.
    Should be called during application startup.
    """
    try:
        # Initialize Cosmos DB
        mongo_instance.initialize()
        logger.info("Successfully initialized Cosmos DB")
    except Exception as e:
        logger.error(f"Failed to initialize Cosmos DB: {str(e)}")
        raise

    try:
        # Initialize MongoDB
        mongo_instance.initialize()
        logger.info("Successfully initialized MongoDB")
    except Exception as e:
        logger.error(f"Failed to initialize MongoDB: {str(e)}")
        raise


def initialize_cosmos_only():
    """
    Initialize only Cosmos DB database and containers.
    """
    try:
        mongo_instance.initialize()
        logger.info("Successfully initialized Cosmos DB")
    except Exception as e:
        logger.error(f"Failed to initialize Cosmos DB: {str(e)}")
        raise


def initialize_mongo_only():
    """
    Initialize only MongoDB database and collections.
    """
    try:
        mongo_instance.initialize()
        logger.info("Successfully initialized MongoDB")
    except Exception as e:
        logger.error(f"Failed to initialize MongoDB: {str(e)}")
        raise
