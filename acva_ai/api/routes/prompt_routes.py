# import logging
# import re
# from fastapi import APIRouter, HTTPException, Depends
# from fastapi.responses import JSONResponse
# from fastapi.encoders import jsonable_encoder
# from typing import Set, Tuple, Dict, List, Optional
# 
# from acva_ai.models import Prompt
# from acva_ai.database.prompts_mongo import prompts_mongo_client
# from acva_ai.utils import verify_api_key
# 
# logger = logging.getLogger(__name__)
# 
# prompt_router = APIRouter()
# 
# 
# def analyze_placeholders(text: str) -> Tuple[int, Set[str]]:
#     """
#     Analyze a prompt text to find unnamed and named placeholders.
# 
#     Args:
#         text: The prompt text to analyze
# 
#     Returns:
#         Tuple containing:
#         - Count of unnamed placeholders ({})
#         - Set of named placeholders ({name})
#     """
#     unnamed_count = text.count("{}")
#     named_placeholders = set(re.findall(r"(?<!\{)\{([^{}]+)\}(?!\})", text))
#     return unnamed_count, named_placeholders
# 
# 
# async def get_all_prompts() -> List[Dict]:
#     """Fetch all prompts from the database."""
#     prompts = prompts_mongo_client.get_all_prompts()
#     return jsonable_encoder(prompts)
# 
# 
# async def get_prompt_by_name_internal(prompt_name: str) -> Optional[Dict]:
#     """Fetch a specific prompt by name from the database."""
#     prompt = prompts_mongo_client.get_prompt(prompt_name)
#     if not prompt:
#         return None
#     return jsonable_encoder(prompt)
# 
# 
# def determine_expected_placeholders(prompt_data: Dict) -> Tuple[int, Set[str]]:
#     """
#     Determine the expected placeholders in a prompt based on its data.
# 
#     Args:
#         prompt_data: The prompt data from the database
# 
#     Returns:
#         Tuple containing:
#         - Expected count of unnamed placeholders
#         - Expected set of named placeholders
#     """
#     expected_unnamed = 0
#     expected_named = set()
# 
#     if (
#         "variables" in prompt_data
#         and isinstance(prompt_data["variables"], list)
#         and prompt_data["variables"]
#     ):
#         expected_named = set(map(str, prompt_data["variables"]))
#         expected_unnamed = 0
#         current_text = prompt_data.get("prompt", "")
#         if current_text.count("{}") > 0:
#             logging.warning(
#                 f"Prompt '{prompt_data.get('name')}' has defined variables but also contains '{{}}' in its text."
#             )
#     elif "prompt" in prompt_data:
#         current_text = prompt_data["prompt"]
#         expected_unnamed, expected_named = analyze_placeholders(current_text)
#         if expected_named and expected_unnamed:
#             logging.warning(
#                 f"Prompt '{prompt_data.get('name')}' text contains both named and unnamed placeholders. Validation may be ambiguous."
#             )
# 
#     return expected_unnamed, expected_named
# 
# 
# def validate_placeholders(
#     prompt_name: str, prompt_text: str, expected_unnamed: int, expected_named: Set[str]
# ) -> List[str]:
#     """
#     Validate that a prompt text contains the expected placeholders.
# 
#     Args:
#         prompt_name: Name of the prompt being validated
#         prompt_text: The new prompt text to validate
#         expected_unnamed: Expected count of unnamed placeholders
#         expected_named: Expected set of named placeholders
# 
#     Returns:
#         List of error messages, empty if validation passes
#     """
#     incoming_unnamed, incoming_named = analyze_placeholders(prompt_text)
# 
#     error_messages = []
#     if incoming_unnamed != expected_unnamed:
#         error_messages.append(
#             f"Expected {expected_unnamed} unnamed placeholders '{{}}', but found {incoming_unnamed}."
#         )
#     if incoming_named != expected_named:
#         missing = expected_named - incoming_named
#         extra = incoming_named - expected_named
#         if missing:
#             error_messages.append(
#                 f"Missing named placeholders: {{{', '.join(sorted(list(missing)))}}}."
#             )
#         if extra:
#             error_messages.append(
#                 f"Unexpected named placeholders: {{{', '.join(sorted(list(extra)))}}}."
#             )
# 
#     return error_messages
# 
# 
# @prompt_router.get("/prompts/")
# async def get_prompts(api_key: str = Depends(verify_api_key)):
#     """
#     Retrieves a list of all available prompts stored in the database.
# 
#     Each prompt object in the list contains details like name, text, description,
#     variables, version history, etc., depending on the database schema for prompts.
#     The response is JSON encoded, handling types like datetime.
#     """
#     prompts = await get_all_prompts()
#     return JSONResponse(status_code=200, content=prompts)
# 
# 
# @prompt_router.get("/prompts/{prompt_name}")
# async def get_prompt_by_name(prompt_name: str, api_key: str = Depends(verify_api_key)):
#     """
#     Retrieves the details of a specific prompt identified by its unique name.
# 
#     - **prompt_name**: The unique name of the prompt to retrieve.
# 
#     Returns the prompt object (JSON encoded) if found, otherwise raises 404.
#     """
#     prompt = await get_prompt_by_name_internal(prompt_name)
#     if not prompt:
#         raise HTTPException(status_code=404, detail=f"Prompt '{prompt_name}' not found")
#     return JSONResponse(status_code=200, content=prompt)
# 
# 
# @prompt_router.post("/prompts/update")
# async def update_prompt(prompt: Prompt, api_key: str = Depends(verify_api_key)):
#     """
#     Updates an existing prompt identified by `prompt.name`.
# 
#     Performs validation to ensure the placeholders in the new prompt text match
#     the expected structure (either named `{variable}` or unnamed `{}`) of the
#     existing prompt version.
# 
#     - **Request Body**: Requires a JSON object conforming to the `Prompt` model
#       (containing `name`, `prompt` text, optional `description`).
#     - **api_key**: API key for authentication.
#     """
#     # Get current prompt data
#     current_prompt_data = prompts_mongo_client.get_prompt(prompt.name)
#     if not current_prompt_data:
#         raise HTTPException(
#             status_code=404, detail=f"Prompt '{prompt.name}' not found. Cannot update."
#         )
# 
#     # Determine expected placeholders
#     try:
#         expected_unnamed, expected_named = determine_expected_placeholders(
#             current_prompt_data
#         )
#     except Exception as e:
#         raise HTTPException(
#             status_code=500,
#             detail=f"Could not determine expected placeholders for existing prompt '{prompt.name}': {str(e)}",
#         )
# 
#     # Validate new prompt text
#     error_messages = validate_placeholders(
#         prompt.name, prompt.prompt, expected_unnamed, expected_named
#     )
# 
#     if error_messages:
#         raise HTTPException(
#             status_code=400,
#             detail=f"Validation failed for prompt '{prompt.name}': {' '.join(error_messages)}",
#         )
# 
#     # Update prompt in database
#     try:
#         result = prompts_mongo_client.add_prompt(
#             prompt.name, prompt.prompt, prompt.description
#         )
#         return JSONResponse(
#             status_code=200,
#             content={
#                 "message": f"Prompt '{prompt.name}' updated successfully.",
#                 "version": result["version"],
#             },
#         )
#     except Exception as e:
#         logger.error(
#             f"Database error updating prompt '{prompt.name}': {e}", exc_info=True
#         )
#         raise HTTPException(
#             status_code=500,
#             detail=f"Database error while updating prompt '{prompt.name}'.",
#         )
