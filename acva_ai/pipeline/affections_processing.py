import asyncio
import logging
import traceback
from typing import Any, Dict, List, Optional, Tuple

from acva_ai.database import mongo_instance
from acva_ai.llm.llm_orchestrator import LLMOrchestrator
from acva_ai.llm.scenarios.affections import extract_affections
from acva_ai.models.affection import Affection
from acva_ai.models.processing_status import ProcessingStatus
from acva_ai.models.visit_report import VisitReport
from acva_ai.services.rag import rag_service
from acva_ai.utils.usage import ResponseUsage

logger = logging.getLogger(__name__)


async def verify_affection_with_rag(
    affection_name: str,
) -> Tuple[bool, Dict[str, Any]]:
    """
    Verify if an affection name corresponds to its context using the RAG system.

    Args:
        affection_name: The name of the affection

    Returns:
        Tuple containing verification result (True if verified) and best match info
    """
    # If no affection name, can't verify
    if not affection_name:
        return False, {}

    # Search for the affection name in the medical terminology
    results = await rag_service.query(
        collection_name="medical_terms_dictionary",
        query_text=affection_name,
        limit=1,
        score_threshold=0.7,
    )

    if not results:
        return False, {}

    best_match = results[0]
    return True, best_match


async def search_transcript_chunks(transcript: str) -> List[Dict[str, Any]]:
    """
    Search chunks of the transcript in the medical terminology RAG.

    Args:
        transcript: The full transcript text

    Returns:
        List of potential affections found in the transcript
    """
    # Split transcript into individual sentences
    sentences = (
        transcript.replace(".", ". ").replace("!", "! ").replace("?", "? ").split(". ")
    )

    # Filter out empty sentences and use each sentence as a separate chunk
    chunks = [sentence.strip() + "." for sentence in sentences if sentence.strip()]

    async def search_chunk(chunk):
        return await rag_service.query(
            collection_name="medical_terms_dictionary",
            query_text=chunk,
            limit=5,  # Increased from 2 to 5
            score_threshold=0.65,
        )

    search_tasks = [search_chunk(chunk) for chunk in chunks]
    all_results = await asyncio.gather(*search_tasks)

    unique_results = []
    seen_terms = set()

    for i, results in enumerate(all_results):
        for result in results:
            term = result.get("payload", {}).get("term", "")
            if term and term not in seen_terms:
                seen_terms.add(term)
                result["chunk_context"] = chunks[i]
                unique_results.append(result)

    return unique_results


async def verify_affections(
    transcript: str,
    llm_orchestrator: LLMOrchestrator,
    response_usage: Optional[ResponseUsage] = None,
) -> List[Affection]:
    """
    Extract and verify affections from a transcript.

    Args:
        transcript: The medical transcript text
        response_usage: Optional usage tracking
        llm_orchestrator: Optional LLM orchestrator for provider selection

    Returns:
        List of verified Affection objects
    """
    # Extract initial affections
    extracted_affections = await extract_affections(
        transcript=transcript,
        llm_orchestrator=llm_orchestrator,
        response_usage=response_usage,
    )

    # Search for additional affections using moving window approach
    additional_terms = await search_transcript_chunks(transcript)

    # Process extracted affections
    affections_list = []
    for affection_name, affection_context in extracted_affections:
        is_verified = False
        verification_info = {}

        # Verify affection against terminology if name is present
        if affection_name:
            is_verified, verification_info = await verify_affection_with_rag(
                affection_name
            )

        # Create affection object with verification info
        if affection_name or affection_context:
            affection = Affection(
                affection_name=affection_name,
                affection_context=affection_context,
                is_verified=is_verified,
                verification_source=(
                    verification_info.get("payload", {}).get("term", "")
                    if verification_info
                    else ""
                ),
                verification_explanation=(
                    verification_info.get("payload", {}).get("explanation", "")
                    if verification_info
                    else ""
                ),
            )
            affections_list.append(affection)

    # Add additional affections found through chunk searching
    for term_info in additional_terms:
        # Check if this term is already in our list
        term = term_info.get("payload", {}).get("term", "")
        if term and not any(
            a.affection_name.lower() == term.lower() for a in affections_list
        ):
            chunk_context = term_info.get("chunk_context", "")
            affection = Affection(
                affection_name=term,
                affection_context=chunk_context,
                is_verified=True,  # Found directly in terminology
                verification_source=term,
                verification_explanation=term_info.get("payload", {}).get(
                    "explanation", ""
                ),
            )
            affections_list.append(affection)

    return affections_list


async def process_affections(
    task_id: str,
    processing_status: ProcessingStatus,
    visit_report: VisitReport,
    llm_orchestrator: LLMOrchestrator,
    response_usage: Optional[ResponseUsage] = None,
):
    """
    Process affections from the transcript

    Args:
        task_id: Unique identifier for the task
        processing_status: Processing status object to update
        visit_report: Visit report object to update
        response_usage: Optional ResponseUsage object to track costs
        llm_orchestrator: Optional LLMOrchestrator to use for LLM calls
    """
    try:
        logger.info(f"[Task {task_id}] Starting affections processing")
        processing_status.start_stage("affections_processing")
        mongo_instance.update_processing_status(task_id, processing_status.dict())
        transcript = (
            visit_report.transcript
            if visit_report.transcript
            else visit_report.raw_transcript
        )

        affections = await verify_affections(
            transcript, llm_orchestrator=llm_orchestrator, response_usage=response_usage
        )
        visit_report.affections = affections
        processing_status.complete_stage("affections_verification", affections)
        logger.info(f"[Task {task_id}] Completed affections verification")

    except Exception as e:
        stack_trace = traceback.format_exc()
        processing_status.fail_stage("affections_verification", e, stack_trace)
        logger.error(f"[Task {task_id}] Error verifying affections: {e}\n{stack_trace}")
    finally:
        mongo_instance.update_processing_status(task_id, processing_status.dict())
