import io
from acva_ai.services.minio_client import MinioClient
from pydub.audio_segment import AudioSegment

def _save_audio_segment_to_minio(
    audio_segment: AudioSegment,
    task_id: str
):
    """
    Save the audio segment to MinIO storage.

    :param audio_segment: The audio segment to save.
    :param task_id: Unique identifier for the task.
    """
    minio_client = MinioClient()
    audio_bytes = io.BytesIO()
    audio_segment.export(audio_bytes, format="wav")
    audio_bytes.seek(0)
    object_name = f"{task_id}.wav"
    minio_client.upload_file(object_name, audio_bytes, length=audio_bytes.getbuffer().nbytes, content_type="audio/wav")


def save_audio_segment(
    audio_segment: AudioSegment,
    task_id: str
):
    """
    Save the audio segment to MinIO storage.

    :param audio_segment: The audio segment to save.
    :param task_id: Unique identifier for the task.
    """
    try:
        _save_audio_segment_to_minio(audio_segment, task_id)
        return {"status": "success", "message": f"Audio segment saved with task ID {task_id}"}
    except Exception as e:
        return {"status": "error", "message": str(e)}