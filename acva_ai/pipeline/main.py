import asyncio
import io
import logging
from typing import List

from fastapi import UploadFile
from pydub import AudioSegment

from acva_ai.database import mongo_instance
from acva_ai.llm.llm_orchestrator import LLMOrchestrator
from acva_ai.llm.scenarios.grammar import grammar_check_by_chunks
from acva_ai.llm.scenarios.medical_report import build_medical_report_with_pipeline
from acva_ai.models.processing_status import ProcessingStatus
from acva_ai.models.visit_report import VisitReport
from acva_ai.pipeline.affections_processing import process_affections
from acva_ai.pipeline.audio_save import save_audio_segment
from acva_ai.pipeline.domain_insights import process_domain_insights
from acva_ai.pipeline.html_generation import convert_domain_insights_to_html
from acva_ai.pipeline.medication_processing import process_medication
from acva_ai.pipeline.transcription import generate_transcription
from acva_ai.services.acva_server import send_callback
from acva_ai.utils.audio_utils import concatenate_audio_files, normalize_audio_segment
from acva_ai.utils.usage import ResponseUsage
from acva_ai._params import DEFAULT_LLM_PROVIDER

# Set up logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


async def proces_visit_audios(
    task_id: str, audio_files: List[UploadFile], primary_provider=LLMOrchestrator
):
    audio_segment = concatenate_audio_files(audio_files)
    audio_segment = normalize_audio_segment(audio_segment)
    await process_visit(
        task_id=task_id, audio_segment=audio_segment, primary_provider=primary_provider
    )


async def process_visit(
    task_id: str,
    audio_segment: AudioSegment,
    primary_provider: LLMOrchestrator,
):
    """
    Main pipeline for processing medical visit audio with enhanced state tracking
    and error handling

    Args:
        task_id: Unique identifier for the task
        audio_segment: Audio segment to process
        primary_provider: Primary LLM provider to use for all LLM calls
    """
    # Initialize audio_processing status and visit report
    processing_status = ProcessingStatus(
        task_id=task_id, overall_status="started", current_stage="started"
    )
    visit_report = VisitReport(task_id=task_id)
    response_usage = ResponseUsage()

    # Add orchestrator to visit report metadata
    visit_report.metadata = {
        "llm_provider": primary_provider.primary_provider,
    }

    # Initialize database state
    mongo_instance.update_processing_status(task_id, processing_status.dict())
    mongo_instance.save_visit_report(task_id, visit_report.dict())

    save_audio_segment(task_id=task_id, audio_segment=audio_segment)

    # Stage 1: Audio Transcription
    await generate_transcription(
        task_id=task_id,
        audio_segment=audio_segment,
        visit_report=visit_report,
        processing_status=processing_status,
        response_usage=response_usage,
    )

    transcript = visit_report.raw_transcript
    await grammar_check_by_chunks(
        task_id=task_id,
        transcript=transcript,
        processing_status=processing_status,
        visit_report=visit_report,
        language="Romanian",
        response_usage=response_usage,
        llm_orchestrator=primary_provider,
    )

    # Run medication, affections, and medical report processing in parallel
    await asyncio.gather(
        process_medication(
            task_id=task_id,
            processing_status=processing_status,
            visit_report=visit_report,
            response_usage=response_usage,
            llm_orchestrator=primary_provider,
        ),
        process_affections(
            task_id=task_id,
            processing_status=processing_status,
            visit_report=visit_report,
            response_usage=response_usage,
            llm_orchestrator=primary_provider,
        ),
        build_medical_report_with_pipeline(
            task_id=task_id,
            transcript=visit_report.transcript,
            processing_status=processing_status,
            visit_report=visit_report,
            llm_orchestrator=primary_provider,
            response_usage=response_usage,
        ),
    )

    await process_domain_insights(
        task_id=task_id,
        processing_status=processing_status,
        visit_report=visit_report,
        response_usage=response_usage,
        llm_orchestrator=primary_provider,
    )

    await convert_domain_insights_to_html(
        task_id=task_id,
        processing_status=processing_status,
        visit_report=visit_report,
        response_usage=response_usage,
        llm_orchestrator=primary_provider,
    )

    logger.info(f"[Task {task_id}] Processing completed")
    processing_status.finalize()
    mongo_instance.update_processing_status(task_id, processing_status.dict())

    visit_report.response_usage = response_usage
    mongo_instance.save_visit_report(task_id, visit_report.dict())

    # Send callback with complete report
    logger.info(f"[Task {task_id}] Sending callback")
    callback_result = await send_callback(task_id=task_id, visit_report=visit_report)
    if callback_result:
        logger.info(f"[Task {task_id}] Callback sent successfully")
    else:
        logger.warning(f"[Task {task_id}] Callback failed")


def process_visit_background(
    task_id: str, audio_segment: AudioSegment, primary_provider: LLMOrchestrator
):
    """
    Synchronous wrapper to run the async process_visit in the background.
    """
    import asyncio

    asyncio.run(
        process_visit(
            task_id=task_id,
            audio_segment=audio_segment,
            primary_provider=primary_provider,
        )
    )


def test():
    sample_audio_path = ".data/samples/recording-2.mp3"
    sample_audio_path = ".data/demo/1e4cd7fa-b4a1-47ce-99aa-abdf66885b2b.wav"
    task_id = "702814b8-2068-4cc1-b8df-4b0ec09e6243"

    audio_segment = AudioSegment.from_file(sample_audio_path)
    result = asyncio.run(process_visit(task_id=task_id, audio_segment=audio_segment))
    print(result)


if __name__ == "__main__":
    test()
