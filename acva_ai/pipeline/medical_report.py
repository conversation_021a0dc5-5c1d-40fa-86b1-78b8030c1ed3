import asyncio
import logging
from typing import Any, Dict, Optional

from acva_ai.llm.scenarios.medical_report import _build_medical_report
from acva_ai.utils.usage import ResponseUsage

logger = logging.getLogger(__name__)


async def build_medical_report(
    transcript: str,
    response_usage: Optional[ResponseUsage] = None,
    llm_orchestrator: Optional["LLMOrchestrator"] = None,
) -> Dict[str, Any]:
    """
    Build a structured medical report from the transcript.

    Args:
        transcript: The medical conversation transcript
        response_usage: Optional ResponseUsage object to track API usage
        llm_orchestrator: Optional LLM orchestrator for provider selection

    Returns:
        Dictionary containing structured medical report sections
    """
    if response_usage is None:
        response_usage = ResponseUsage()

    # Use the internal function to build the medical report
    return await _build_medical_report(
        transcript=transcript,
        llm_orchestrator=llm_orchestrator,
        response_usage=response_usage,
    )


def test():
    transcript = """Colecist, 6 cm, locul drept, 14 cm, aspect omogen, hiperecogen cu atenuare posterioră, făr<PERSON> dilata<PERSON>ii, venă portă normală.Așa că vorbim de hepatopatie.Da.Ok.Trebuie să specific corect.```\nAm înțeles, gata, organul, orto.\n``````\nPereți normal, conținut transsonic, cu prezența unei imagini hiperecogene, cu diametru bine delimitată, cu diametru de 2,2 cm, cu fond de umbră posterioră.\n```Splina, diametru anteroposterior de 14 cm, omogenă normoecogenă, linic drept, normal situat, contur normal, la nivelul medulare, prezența unor imagini uniforme hiperecogene, fără fond de umbră posterior, fără dilatație ale sistemului pielocalicial.```\nLinicul stâng, poziție normală, contur normal, cu prezența la nivelul medulare unei imagini transsonice bine delimitată.\n```Conținut transsonic de 3,3 cm, fără retenție de urină, în vezica urinară.```\nVezică urinară semirepleție, conținut transsonic, fără formațiuni protruizive.\n``````\nGroscată omogenă, vascularizație normală, cu prezența unei imagini mai hiperecogene, situată central, cu diametru de 2,3 cm.\n``````\nStaatoză hepatică gradul 2, litiază veziculară, nefrolitiază, chist renal stâng.\n```"""
    result = asyncio.run(build_medical_report(transcript=transcript))
    print(result)


if __name__ == "__main__":
    test()
