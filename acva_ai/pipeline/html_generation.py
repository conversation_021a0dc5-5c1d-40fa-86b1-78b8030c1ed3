import logging
import traceback
from typing import List, Optional

from acva_ai.database import mongo_instance
from acva_ai.models.domain_insight import DomainInsight
from acva_ai.models.processing_status import ProcessingStatus
from acva_ai.models.visit_report import VisitReport
from acva_ai.utils.usage import ResponseUsage
from acva_ai.llm.llm_orchestrator import LLMOrchestrator

logger = logging.getLogger(__name__)


async def _convert_domain_insights_to_html(
    domain_insights: List[DomainInsight],
    orchestrator: LLMOrchestrator,
    response_usage: Optional[ResponseUsage] = None,
) -> str:
    """
    Convert domain insights to an HTML format using an LLM prompt.

    Args:
        domain_insights: List of DomainInsight objects with processed content
        response_usage: Optional ResponseUsage object to track API usage

    Returns:
        HTML string representation of the domain insights
    """
    # Create a structured representation of the insights for the prompt
    insights_text = []
    for insight in domain_insights:
        if not insight.insight_content:
            continue

        insights_text.append(f"##\n{insight.insight_content}")

    if not insights_text:
        return ""

    insights_formatted = "\n\n".join(insights_text)

    # TODO this should be moved to a separate script inside llm/scenarios
    # Create the prompt for HTML conversion
    html_conversion_prompt = f"""
        Ai aceste sectiuni: {insights_formatted}. 
        Formateaza continutul intr-un bloc HMTL valid, conținând sectiunile de mai sus formatat dupa cum urmeaza:
   
        Fiecare secțiune trebuie să înceapă cu un titlu H2 si sa fie urmata de un paragraf cu detalii sub forma de bullet points.
        Fiecare rand al notei trebuie sa fie in interiorul unui paragraf, separat de restul paragrafelor prin <br/>;
        Nu include tag-urile <html>, <head>, <body> .
        Inlocuieste toate ghilimelele cu <em>.
        Elimina "\n" si "\n\n" din text.
        Nu include nicio altă etichetă în afara  <h2> ,<p>, <em>, <br/>.
        Asigura-te ca HTML este valid si formatat corect. 
        Returneaza doar codul HTML fara comentarii suplimentare. 
    """

    # Call the LLM to generate the HTML
    html_result = await orchestrator.call_llm(
        prompt=html_conversion_prompt,
        response_usage=response_usage,
        max_tokens=16384,  # Increase token limit for HTML generation
    )

    # Clean up the response if needed
    if html_result.startswith("```html"):
        html_result = html_result.replace("```html", "", 1)
    if html_result.endswith("```"):
        html_result = html_result.rsplit("```", 1)[0]

    return html_result.strip()


async def convert_domain_insights_to_html(
    task_id: str,
    processing_status: ProcessingStatus,
    visit_report: VisitReport,
    llm_orchestrator: LLMOrchestrator,
    response_usage: Optional[ResponseUsage] = None,
):
    """
    Convert domain insights to HTML format

    Args:
        task_id: Unique identifier for the task
        processing_status: Processing status object to update
        visit_report: Visit report object to update
        response_usage: Optional ResponseUsage object to track costs
        llm_orchestrator: Optional LLMOrchestrator to use for LLM calls
    """
    try:
        logger.info(f"[Task {task_id}] Starting HTML generation")
        processing_status.start_stage("html_generation")
        mongo_instance.update_processing_status(task_id, processing_status.dict())

        if not visit_report.domain_insights:
            raise ValueError(
                f"No domain insights found for task {task_id}. Cannot generate HTML."
            )

        domain_insights_processed_html = await _convert_domain_insights_to_html(
            domain_insights=visit_report.domain_insights,
            response_usage=response_usage,
            orchestrator=llm_orchestrator,
        )

        visit_report.html_note = domain_insights_processed_html
        processing_status.complete_stage(
            "html_generation", domain_insights_processed_html
        )
        logger.info(f"[Task {task_id}] Completed HTML generation")
    except Exception as e:
        stack_trace = traceback.format_exc()
        processing_status.fail_stage("html_generation", e, stack_trace)
        logger.error(
            f"[Task {task_id}] Error converting domain insights to HTML: {e}\n{stack_trace}"
        )
    finally:
        mongo_instance.update_processing_status(task_id, processing_status.dict())
