import json
from datetime import datetime
from typing import List, Optional, Any
from uuid import UUID

from bson import ObjectId

from acva_ai.models.domain_insight import DomainInsight
import httpx
from acva_ai._params import ACVA_REPORT_FIELDS, API_KEY, ACVA_CALLBACK_URL
import logging

from acva_ai.models.visit_report import VisitReport


logger = logging.getLogger(__name__)


def retrieve_domain_insights(task_id: str) -> List[DomainInsight]:
    response = httpx.get(
        url=ACVA_REPORT_FIELDS.format(task_id=task_id),
        headers={"X-API-Key": API_KEY},
    )

    domain_insights = []
    if response.status_code == 200:
        for key, value in response.json().get("prompts").items():
            domain_insight = DomainInsight(
                insight_name=key,
                insight_extraction_prompt=value
            )
            domain_insights.append(domain_insight)

    return domain_insights


def test():
    task_id = "702814b8-2068-4cc1-b8df-4b0ec09e6243"
    # TODO test with a valid task_id
    print(retrieve_domain_insights(task_id))


if __name__ == '__main__':
    test()


class CustomJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder that handles ObjectId, UUID, and datetime objects."""

    def default(self, obj: Any) -> Any:
        if isinstance(obj, (ObjectId, UUID)):
            return str(obj)
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)


async def send_callback(task_id, visit_report: VisitReport):
    """
    Sends a callback with the medical report to the configured endpoint.

    Args:
        task_id: The unique identifier for the task.
        visit_report: The visit report to send.

    Returns:
        bool: True if the callback was successful, False otherwise.
    """
    try:
        # Get the report data using model_dump
        visit_report_data = visit_report.model_dump()

        # Prepare the payload
        payload = {
            "task_id": task_id,
            "medical_report": visit_report_data,
        }

        # Use the custom encoder to serialize the JSON
        json_payload = json.dumps(payload, cls=CustomJSONEncoder)

        async with httpx.AsyncClient() as client:
            response = await client.post(
                ACVA_CALLBACK_URL.format(task_id=task_id),
                content=json_payload,
                headers={"X-API-Key": API_KEY, "Content-Type": "application/json"},
            )
            return response.status_code == 200
    except Exception as e:
        logger.error(f"[Task {task_id}] Callback failed: {e}")
        return False
