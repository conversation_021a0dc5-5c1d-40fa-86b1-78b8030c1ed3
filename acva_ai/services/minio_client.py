from minio import Minio
from minio.error import S3Error
import io
from acva_ai._params import MINIO_SERVER, MINIO_ROOT_USER, MINIO_ROOT_PASSWORD, MINIO_PORT

class MinioClient:
    def __init__(
            self,
            endpoint=f"{MINIO_SERVER}:9000", 
            access_key=MINIO_ROOT_USER,
            secret_key=MINIO_ROOT_PASSWORD, 
            secure=False, 
            bucket_name="acva-audio"
    ):
        self.client = Minio(endpoint, access_key=access_key, secret_key=secret_key, secure=secure)
        self.bucket_name = bucket_name
        self._ensure_bucket()

    def _ensure_bucket(self):
        if not self.client.bucket_exists(self.bucket_name):
            self.client.make_bucket(self.bucket_name)

    def upload_file(self, object_name, data, length, content_type="application/octet-stream"):
        self.client.put_object(self.bucket_name, object_name, data, length, content_type=content_type)

    def download_file(self, object_name):
        response = self.client.get_object(self.bucket_name, object_name)
        return response.read()

    def list_files(self, prefix=""):
        return [obj.object_name for obj in self.client.list_objects(self.bucket_name, prefix=prefix)]

    def delete_file(self, object_name):
        self.client.remove_object(self.bucket_name, object_name)

# Example usage:
# minio_client = MinioClient()
# with open("test.wav", "rb") as f:
#     minio_client.upload_file("test.wav", f, length=os.path.getsize("test.wav"), content_type="audio/wav")

