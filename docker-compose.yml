services:
  backend:
    container_name: backend
    build:
      context: .
      dockerfile: resources/Dockerfile
    ports:
      - "8000:8000"
    env_file:
      - .env
    environment:
      - QDRANT_SERVER=qdrant_db
      - MINIO_SERVER=minio
      - TMP_DATA_DIR=/tmp
    volumes:
      - ${TMP_DATA_DIR}:/tmp
      - ./data:/app/data
    depends_on:
      - qdrant_db
    command: >
      /bin/sh -c "gunicorn acva_ai.api.main:app -b 0.0.0.0:8000 --workers ${API_WORKERS} --threads ${API_WORKER_THREADS} -k 'uvicorn.workers.UvicornWorker' --timeout 600"

  qdrant_db:
    image: qdrant/qdrant
    container_name: qdrant_db
    ports:
      - "6333:6333"
    volumes:
      - ${QDRANT_FOLDER}:/qdrant/storage
    healthcheck:
      test: ["CMD", "wget", "--spider", "-q", "http://localhost:6333/collections"]
      interval: 5s
      retries: 5

  minio:
    image: minio/minio:latest
    container_name: minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: $MINIO_ROOT_USER
      MINIO_ROOT_PASSWORD: $MINIO_ROOT_PASSWORD
    command: server /data --console-address ":9001"
    volumes:
      - ${MINIO_FOLDER}:/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

volumes:
  minio_data:
    driver: local
