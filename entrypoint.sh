#!/bin/bash
set -e # Exit immediately if a command exits with a non-zero status.

# Start the PostgreSQL service in the background
echo "Starting PostgreSQL service..."
service postgresql start

# Optional: Check if Postgres is ready (simple check)
# Loop until Postgres is responsive or timeout
timeout=10
while ! pg_isready -q -h localhost -p 5432 -U postgres; do
  sleep 1
  timeout=$((timeout - 1))
  if [ $timeout -eq 0 ]; then
    echo "PostgreSQL did not become ready in time."
    exit 1
  fi
done
echo "PostgreSQL service is ready."


# Execute the main container command (passed from Dockerfile CMD or docker run)
# Use exec to replace the script process with the python process
echo "Starting application: $@"
exec "$@"
