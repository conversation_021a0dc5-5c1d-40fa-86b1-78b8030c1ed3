# ACVA-AI API Documentation

## Overview

The ACVA-AI API provides a comprehensive set of endpoints for processing medical audio transcriptions. The API follows RESTful design principles and requires authentication via API key.

## Authentication

All API endpoints require an API key to be provided in the request header:

```
X-API-Key: your_api_key_here
```

## Endpoints

### Individual Pipeline Components

#### 1. Transcription

**POST /transcription/transcribe**

Transcribes an audio file and returns the raw transcript.

- **Request**: Multipart form with audio file
- **Response**: JSON with task_id and transcript

#### 2. Grammar Correction

**POST /grammar/correct-grammar**

Corrects grammar and improves the quality of a transcript.

- **Request**: Plain text transcript
- **Response**: JSON with corrected transcript

#### 3. Medication Extraction

**POST /medication/extract-medications**

Extracts medication information from a transcript.

- **Request**: Plain text transcript
- **Response**: JSON with list of medications

#### 4. Affection Extraction

**POST /affection/extract-affections**

Extracts medical conditions and affections from a transcript.

- **Request**: Plain text transcript
- **Response**: <PERSON><PERSON><PERSON> with list of affections

#### 5. Domain Insights Processing

**POST /domain/process-domain-insights**

Processes domain-specific insights from a transcript.

- **Request**: Plain text transcript and list of domain insights
- **Response**: JSON with processed domain insights

### Complete Pipeline

#### Process Audio Pipeline

**POST /pipeline/process-audio**

Processes an audio file through the complete medical transcription pipeline.

- **Request**: Multipart form with audio file and optional domain_id
- **Response**: JSON with complete processing results

#### Process Transcript Pipeline

**POST /pipeline/process-transcript**

Processes a transcript through the medical transcription pipeline (skipping audio transcription).

- **Request**: Plain text transcript and optional domain_id
- **Response**: JSON with complete processing results

## Error Handling

All endpoints follow a consistent error handling pattern:

- **400 Bad Request**: Invalid input parameters
- **401 Unauthorized**: Invalid or missing API key
- **500 Internal Server Error**: Processing error

Error responses include a detail message explaining the error.

## Project Onboarding Guide

### 1. Project Overview and Purpose

ACVA-AI is a comprehensive medical consultation processing system designed to transform audio recordings of medical consultations into structured, enhanced medical reports. The system acts as an AI Medical Scribe that:

- **Transcribes** audio consultations with high accuracy using advanced AI services
- **Extracts** medications, symptoms, and medical conditions from transcripts
- **Enhances** content with medical knowledge from databases and web searches
- **Generates** structured medical reports with domain-specific insights
- **Integrates** seamlessly with existing medical workflows

The primary goal is to reduce the administrative burden on healthcare professionals by automating the documentation process while improving accuracy and completeness of medical records.

### 2. System Architecture

ACVA-AI follows a modular architecture with several specialized components:

#### Core Components

1. **Audio Processing Module**
   - Handles audio file ingestion and preprocessing
   - Splits audio into manageable chunks for transcription
   - Manages audio format conversion and quality optimization

2. **Transcription Engine**
   - Converts speech to text with high accuracy
   - Provides word-level confidence scores and timestamps
   - Supports multiple transcription services (OpenAI Whisper, Vatis API)

3. **Text Enhancement Pipeline**
   - Corrects grammar and improves transcript quality
   - Identifies and resolves low-confidence segments
   - Standardizes medical terminology

4. **Medical Entity Extraction**
   - Identifies medications, dosages, and administration routes
   - Extracts symptoms, conditions, and diagnoses
   - Recognizes medical procedures and tests

5. **Knowledge Augmentation System**
   - Retrieval Augmented Generation (RAG) for medical knowledge
   - Vector database for semantic search of medical information
   - Web search integration for medication details and references

6. **Report Generation Engine**
   - Structures information into standardized medical report formats
   - Generates domain-specific insights based on transcript content
   - Creates HTML-formatted reports for easy integration

7. **API Layer**
   - RESTful endpoints for system interaction
   - Authentication and authorization
   - Asynchronous task management

8. **Storage and Database Layer**
   - Document storage for transcripts and reports
   - Vector database for embeddings and semantic search
   - Relational database for structured data and relationships

### 3. Technology Stack

#### Backend Framework
- **Python 3.8+**: Core programming language
- **FastAPI**: Modern, high-performance web framework
- **Uvicorn/Gunicorn**: ASGI server implementation

#### Databases
- **MongoDB**: Document storage for transcripts and reports
- **PostgreSQL**: Relational database for structured data
- **Qdrant**: Vector database for embeddings and semantic search

#### AI and ML Services
- **OpenAI GPT Models**: For text generation and enhancement
- **Azure OpenAI**: For production-grade AI services
- **OpenAI Whisper**: For audio transcription
- **Vatis API**: Alternative transcription service

#### External Services
- **Brave Search API**: For web search capabilities
- **Firecrawl API**: For web scraping and content extraction

#### Infrastructure
- **Docker**: Containerization
- **Docker Compose**: Multi-container orchestration
- **Async I/O**: For non-blocking operations

#### Development Tools
- **Poetry**: Dependency management
- **Black**: Code formatting
- **isort**: Import sorting
- **pytest**: Testing framework

### 4. Key Features

#### 🎵 Audio Processing
- **Dual Transcription Engines**: OpenAI Whisper and Vatis API support
- **Intelligent Audio Splitting**: Automatically divides long recordings into optimal chunks
- **Word-level Accuracy**: Timestamps and confidence scores for each word
- **Quality Assurance**: Automatic correction of low-confidence segments
- **Multi-format Support**: Processes WAV, MP3, and other common audio formats

#### 🧠 AI-Powered Enhancement
- **Medical Entity Extraction**: Automatic identification of medications and conditions
- **Context-Aware Corrections**: Uses surrounding text for better accuracy
- **Knowledge Augmentation**: RAG system with medical terminology database
- **Multi-language Support**: Primary Romanian with English fallbacks
- **Grammar Correction**: Fixes transcription errors and improves readability

#### 🔍 Intelligent Search
- **Medication Information**: Automatic web search for drug prospects and references
- **Medical Knowledge Base**: Vector database search for symptoms and conditions
- **Real-time Enhancement**: Live integration with medical information sources
- **Semantic Search**: Finds related medical concepts even when terms differ

#### 📋 Structured Reporting
- **Standardized Medical Reports**: Generates reports following medical documentation best practices
- **Domain-Specific Insights**: Extracts specialized information based on configurable prompts
- **HTML Formatting**: Creates visually structured reports ready for integration
- **Customizable Sections**: Configurable report sections based on medical specialty
- **Callback Integration**: Sends completed reports to external systems

### 5. Processing Pipeline

The ACVA-AI processing pipeline follows these sequential steps:

1. **Audio Ingestion**
   - Audio file is uploaded through the API
   - System validates format and quality
   - A unique task ID is assigned for tracking

2. **Audio Preprocessing**
   - Audio is converted to a standard format if needed
   - Long recordings are split into optimal chunks based on silence detection
   - Audio quality is assessed for transcription suitability

3. **Transcription**
   - Audio chunks are sent to transcription services in parallel
   - Word-level transcription with confidence scores is generated
   - Chunks are reassembled into a complete transcript

4. **Grammar Correction**
   - Transcript is divided into manageable text chunks
   - Each chunk is processed for grammar and spelling correction
   - Special attention is given to medical terminology
   - Corrected chunks are reassembled

5. **Medical Entity Extraction**
   - Medications are identified with names, dosages, and routes
   - Medical conditions and symptoms are extracted
   - Extracted entities are verified against medical databases

6. **Knowledge Augmentation**
   - Medication information is enhanced through web searches
   - Medical conditions are enriched with information from the knowledge base
   - RAG techniques are used to add relevant medical context

7. **Domain Insight Processing**
   - Configurable domain-specific insights are extracted
   - Specialized medical information is organized by categories
   - Insights are formatted according to medical documentation standards

8. **Report Generation**
   - Structured medical report is created with standard sections
   - HTML formatting is applied for visual organization
   - Report is enriched with extracted and augmented information

9. **Result Storage and Callback**
   - Complete results are stored in the database
   - Processing metrics and usage statistics are recorded
   - Results are sent to the configured callback URL

Here's a code example showing the main pipeline orchestration:

```python
async def process_visit(task_id: str, audio_file_path: str):
    """Main pipeline for audio_processing a medical visit recording."""
    # Initialize report and usage tracking
    visit_report = VisitReport(task_id=task_id, status="started")
    response_usage = ResponseUsage()
    
    # Step 1: Transcribe audio
    raw_transcript = await transcribe_audio_with_splitting(
        audio_file_path, response_usage=response_usage
    )
    
    # Step 2: Correct grammar
    corrected_transcript, explanations = await _grammar_check_by_chunks(
        transcript=raw_transcript, response_usage=response_usage
    )
    
    # Step 3: Extract medications and conditions
    medication = await verify_medication(
        corrected_transcript, response_usage=response_usage
    )
    affections = await verify_affections(
        corrected_transcript, response_usage=response_usage
    )
    
    # Step 4: Build medical report
    medical_report = await build_medical_report(
        transcript=corrected_transcript, response_usage=response_usage
    )
    
    # Step 5: Process domain insights
    domain_insights = await fetch_domain_insights(task_id)
    domain_insights_processed = await process_domain_insights(
        transcript=corrected_transcript,
        domain_insights=domain_insights,
        medications=medication,
        affections=affections,
        response_usage=response_usage,
    )
    
    # Step 6: Generate HTML report
    html_report = await convert_domain_insights_to_html(
        domain_insights=domain_insights_processed,
        response_usage=response_usage,
    )
    
    # Step 7: Save results and send callback
    visit_report.update_with_results(
        raw_transcript, corrected_transcript, medical_report,
        medication, affections, domain_insights_processed, html_report
    )
    mongo_instance.update_visit_report(visit_report.model_dump())
    await send_callback(task_id=task_id, medical_report=visit_report.model_dump())
```

### 6. Code Organization

The ACVA-AI project follows a modular structure organized by functionality:

```
acva_ai/
├── api/                  # API layer and endpoints
│   ├── main.py           # FastAPI application entry point
│   └── routes/           # API route definitions
├── database/             # Database connections and operations
│   ├── mongo.py          # MongoDB client and operations
│   └── postgres.py       # PostgreSQL client and operations
├── llm/                  # Language model integration
│   ├── azure_call.py     # Azure OpenAI API integration
│   ├── azure_audio_call.py # Audio transcription with Azure
│   ├── helpers.py        # Utility functions for LLM operations
│   └── scenarios/        # Prompt templates and LLM use cases
├── models/               # Data models and schemas
│   ├── affection.py      # Medical condition models
│   ├── domain_insight.py # Domain-specific insight models
│   ├── medication.py     # Medication models
│   └── visit_report.py   # Complete visit report model
├── pipeline/             # Processing pipeline components
│   ├── affections_processing.py # Medical condition extraction
│   ├── domain_insights.py # Domain insight processing
│   ├── main.py           # Main pipeline orchestration
│   ├── medical_report.py # Medical report generation
│   └── medication_processing.py # Medication extraction
├── processing/           # Core processing functionality
│   ├── audio.py          # Audio file processing
│   └── search.py         # Web search integration
├── services/             # Service integrations
│   ├── rag.py            # Retrieval Augmented Generation
│   └── transcription.py  # Audio transcription services
├── utils/                # Utility functions
│   ├── callback_utils.py # Callback handling
│   ├── general_utils.py  # General utilities
│   ├── prompts.py        # Prompt templates
│   └── usage.py          # API usage tracking
└── _params.py            # Global parameters and configuration
```

#### Key Modules

- **api/**: Contains the FastAPI application and route definitions
- **pipeline/**: Implements the core processing pipeline components
- **llm/**: Manages interactions with language models
- **services/**: Provides integrations with external services
- **models/**: Defines data structures and schemas
- **utils/**: Contains utility functions and helpers

### 7. API Documentation

ACVA-AI exposes a RESTful API for interacting with the system. All endpoints require authentication via an API key provided in the request header:

```
X-API-Key: your_api_key_here
```

#### Core Endpoints

##### Audio Processing

```http
POST /audio/process
Content-Type: multipart/form-data

# Upload audio file for processing
# Returns task_id for status tracking
```

Example request:
```python
import requests

files = {'file': open('consultation.wav', 'rb')}
headers = {'X-API-Key': 'your_api_key'}

response = requests.post(
    'http://localhost:8000/audio/process',
    files=files,
    headers=headers
)

task_id = response.json()['task_id']
```

##### Task Management

```http
GET /task/{task_id}/status
# Check processing status

GET /task/{task_id}/result
# Retrieve processing results
```

Example status check:
```python
response = requests.get(
    f'http://localhost:8000/task/{task_id}/status',
    headers={'X-API-Key': 'your_api_key'}
)

status = response.json()['status']  # "audio_processing", "done", or "error"
```

##### Medical Reports

```http
GET /medical-note/{task_id}
# Get enhanced medical report

POST /medical-note/enhance
# Trigger report enhancement
```

#### Individual Pipeline Components

The API also provides endpoints for accessing individual components of the pipeline:

- **POST /transcription/transcribe**: Transcribe audio only
- **POST /grammar/correct-grammar**: Correct grammar in a transcript
- **POST /medication/extract-medications**: Extract medications from text
- **POST /affection/extract-affections**: Extract medical conditions
- **POST /domain/process-domain-insights**: Process domain insights

#### Response Format

```json
{
  "task_id": "uuid",
  "status": "audio_processing|done|error",
  "transcript": "processed_transcript",
  "simptome": ["list_of_symptoms"],
  "medicamente": {
    "medicament": [
      {
        "denumire": "medication_name",
        "prospect": "drug_information",
        "referinta": "reference_url"
      }
    ]
  },
  "observatii": ["processing_observations"],
  "medical_report": {
    "section1": "enhanced_content",
    "section2": "enhanced_content"
  }
}
```

### 8. Environment Setup

#### Prerequisites

- Docker and Docker Compose
- Python 3.8+ (for local development)
- API keys for required services

#### Required API Keys

```bash
# Core Services
OPENAI_API=your_openai_api_key
API_KEY_VETIS=your_vatis_api_key

# Search Services
BRAVE_API=your_brave_search_api_key
FIRECRAWL_API=your_firecrawl_api_key

# Internal Authentication
API_KEY=your_internal_api_key

# External Integration
ACVA_CALLBACK_URL=your_callback_endpoint
ACVA_REPORT_FIELDS=your_report_fields_endpoint
```

#### Database Configuration

```bash
# MongoDB
MONGODB=********************************:port/database

# PostgreSQL
DB_HOST=localhost
DB_PORT=5432
DB_NAME=acva
DB_USER=postgres
DB_PASSWORD=password

# Qdrant Vector Database
QDRANT_SERVER=localhost
```

#### Installation Steps

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd acva-ai
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and configuration
   ```

3. **Build and start with Docker**
   ```bash
   docker-compose up -d
   ```

4. **For local development**
   ```bash
   # Create a virtual environment
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   
   # Install dependencies
   pip install -r resources/requirements.txt
   
   # Run the application
   python -m acva_ai.api.main
   ```

5. **Verify installation**
   ```bash
   curl http://localhost:8000/health
   # Should return {"status": "ok"}
   ```

### 9. Development Workflow

#### Running Tests

```bash
# Run all tests
python -m pytest tests/

# Run specific test file
python -m pytest tests/test_transcription.py

# Run with coverage
python -m pytest --cov=acva_ai tests/
```

#### Code Formatting

```bash
# Format code with Black
black acva_ai/

# Sort imports
isort acva_ai/

# Run both
black acva_ai/ && isort acva_ai/
```

#### Adding New Features

1. **Create a new branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Implement your changes**
   - Follow the existing code structure
   - Add appropriate tests
   - Update documentation

3. **Run tests and formatting**
   ```bash
   black acva_ai/ && isort acva_ai/
   python -m pytest tests/
   ```

4. **Submit a pull request**
   - Provide a clear description of your changes
   - Reference any related issues

#### Adding New Pipeline Components

To add a new component to the processing pipeline:

1. Create a new module in the appropriate directory (e.g., `acva_ai/pipeline/new_component.py`)
2. Implement the component functionality
3. Add any necessary prompts to `acva_ai/utils/prompts.py`
4. Integrate the component into
