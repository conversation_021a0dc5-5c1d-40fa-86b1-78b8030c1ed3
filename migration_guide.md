# Migration Guide: From Current Architecture to Clean Architecture

## 🎯 **Migration Strategy Overview**

This guide provides a step-by-step approach to migrate your current ACVA-AI codebase to the new clean architecture without breaking existing functionality.

## 📋 **Phase 1: Foundation Setup (Week 1-2)**

### **Step 1: Create New Directory Structure**
```bash
# Create new directory structure alongside existing code
mkdir -p src/domain/entities
mkdir -p src/domain/value_objects
mkdir -p src/domain/repositories
mkdir -p src/domain/services
mkdir -p src/application/use_cases
mkdir -p src/application/commands
mkdir -p src/application/queries
mkdir -p src/infrastructure/database
mkdir -p src/infrastructure/external_apis
mkdir -p src/infrastructure/config
mkdir -p src/presentation/api/controllers
mkdir -p src/presentation/dto
mkdir -p src/shared/exceptions
```

### **Step 2: Define Value Objects First**
```python
# Start with simple value objects that can be used immediately
# src/domain/value_objects/task_id.py
from dataclasses import dataclass
from uuid import UUID

@dataclass(frozen=True)
class TaskId:
    value: UUID
    
    def __str__(self) -> str:
        return str(self.value)

# Use in existing code gradually:
# OLD: task_id: UUID
# NEW: task_id: TaskId
```

### **Step 3: Extract Configuration**
```python
# src/infrastructure/config/settings.py
from pydantic import BaseSettings

class Settings(BaseSettings):
    openai_api_key: str
    mongodb_url: str
    # ... other settings
    
    class Config:
        env_file = ".env"

# Replace acva_ai/_params.py gradually
```

## 📋 **Phase 2: Repository Pattern (Week 3-4)**

### **Step 4: Create Repository Interfaces**
```python
# src/domain/repositories/consultation_repository.py
from abc import ABC, abstractmethod
from typing import Optional
from ..entities.consultation import MedicalConsultation
from ..value_objects import TaskId

class ConsultationRepository(ABC):
    @abstractmethod
    async def save(self, consultation: MedicalConsultation) -> None:
        pass
    
    @abstractmethod
    async def get_by_id(self, task_id: TaskId) -> Optional[MedicalConsultation]:
        pass
```

### **Step 5: Create Repository Adapters**

```python
# src/infrastructure/database/mongo_consultation_repository.py
from ...domain.repositories.consultation_repository import ConsultationRepository
from acva_ai.database import mongo_instance  # Import existing


class MongoConsultationRepository(ConsultationRepository):
    def __init__(self):
        self.mongo = mongo_instance  # Use existing connection

    async def save(self, consultation: MedicalConsultation) -> None:
        # Convert domain object to mongo document
        document = self._to_document(consultation)
        await self.mongo.insert_consultation(document)

    async def get_by_id(self, task_id: TaskId) -> Optional[MedicalConsultation]:
        document = await self.mongo.get_consultation(str(task_id))
        if document:
            return self._to_domain_object(document)
        return None
```

### **Step 6: Gradual Repository Migration**
```python
# In existing acva_ai/services/tasks_(deprecated).py, start using repositories
from src.infrastructure.database.mongo_consultation_repository import MongoConsultationRepository

async def process_task(wav_file_path, transcript_path, task_id, model):
    # OLD: mongo_instance.update_task_status(task_id, "started")
    
    # NEW: Use repository
    repo = MongoConsultationRepository()
    consultation = await repo.get_by_id(TaskId(task_id))
    if consultation:
        consultation.mark_as_started()
        await repo.save(consultation)
```

## 📋 **Phase 3: Service Interfaces (Week 5-6)**

### **Step 7: Extract Service Interfaces**
```python
# src/domain/services/transcription_service.py
from abc import ABC, abstractmethod
from ..value_objects import AudioFile, TaskId

class TranscriptionService(ABC):
    @abstractmethod
    async def transcribe_audio(self, audio_file: AudioFile, task_id: TaskId) -> TranscriptionResult:
        pass
```

### **Step 8: Wrap Existing Services**

```python
# src/infrastructure/external_apis/legacy_openai_service.py
from acva_ai.processing.audio import ProcessAudioOpenAI  # Import existing
from ...domain.services.transcription_service import TranscriptionService


class LegacyOpenAITranscriptionService(TranscriptionService):
    """Adapter that wraps existing ProcessAudioOpenAI"""

    async def transcribe_audio(self, audio_file: AudioFile, task_id: TaskId) -> TranscriptionResult:
        # Use existing implementation
        processor = ProcessAudioOpenAI(
            input_path=[audio_file.file_path],
            output_path=f"/tmp/{task_id}",
            task_id=task_id.value
        )

        await processor.transcribe()

        # Convert result to new format
        return TranscriptionResult(...)
```

## 📋 **Phase 4: Use Cases (Week 7-8)**

### **Step 9: Create Simple Use Cases**
```python
# src/application/use_cases/get_consultation_status_use_case.py
from ...domain.repositories.consultation_repository import ConsultationRepository
from ...domain.value_objects import TaskId

class GetConsultationStatusUseCase:
    def __init__(self, consultation_repo: ConsultationRepository):
        self.consultation_repo = consultation_repo
    
    async def execute(self, task_id: TaskId) -> ConsultationStatusResult:
        consultation = await self.consultation_repo.get_by_id(task_id)
        if not consultation:
            raise ConsultationNotFoundError(f"Consultation {task_id} not found")
        
        return ConsultationStatusResult(
            task_id=consultation.id,
            status=consultation.status,
            progress=self._calculate_progress(consultation.status)
        )
```

### **Step 10: Migrate API Endpoints One by One**
```python
# src/presentation/api/controllers/consultation_controller.py
from fastapi import APIRouter, Depends
from ....application.use_cases.get_consultation_status_use_case import GetConsultationStatusUseCase

router = APIRouter()

@router.get("/{task_id}/status")
async def get_status(
    task_id: str,
    use_case: GetConsultationStatusUseCase = Depends(get_consultation_status_use_case)
):
    result = await use_case.execute(TaskId(UUID(task_id)))
    return result.to_dict()

# Update main FastAPI app to include new routes alongside old ones
app.include_router(router, prefix="/api/v2")  # New endpoints
# Keep existing routes for backward compatibility
```

## 📋 **Phase 5: Dependency Injection (Week 9-10)**

### **Step 11: Simple DI Container**
```python
# src/infrastructure/di/simple_container.py
class Container:
    def __init__(self):
        self._instances = {}
    
    def register(self, interface, implementation):
        self._instances[interface] = implementation
    
    def get(self, interface):
        return self._instances[interface]

# Initialize in main.py
container = Container()
container.register(ConsultationRepository, MongoConsultationRepository())

def get_consultation_repo():
    return container.get(ConsultationRepository)
```

### **Step 12: Gradual DI Migration**
```python
# Replace direct instantiation gradually
# OLD: mongo_instance.update_task_status(...)
# NEW: repo = get_consultation_repo(); await repo.save(...)
```

## 📋 **Phase 6: Domain Logic (Week 11-12)**

### **Step 13: Extract Domain Entities**
```python
# src/domain/entities/consultation.py
class MedicalConsultation:
    def __init__(self, id: TaskId, status: ConsultationStatus, ...):
        self.id = id
        self.status = status
        # ... other fields
    
    def mark_as_transcribing(self):
        if self.status != ConsultationStatus.PENDING:
            raise InvalidStatusTransitionError(...)
        self.status = ConsultationStatus.TRANSCRIBING
    
    def can_process_text(self) -> bool:
        return self.status == ConsultationStatus.TRANSCRIBING and self.transcript is not None
```

### **Step 14: Move Business Logic to Entities**
```python
# OLD: In services/tasks_(deprecated).py
# if chunk["avg_confidence"] < 0.80:
#     # correction logic

# NEW: In domain entity
class TranscriptChunk:
    def needs_correction(self) -> bool:
        return self.confidence.is_low_confidence()
    
    def apply_correction(self, corrected_text: str) -> None:
        if not self.needs_correction():
            raise BusinessRuleViolationError("Chunk doesn't need correction")
        self.corrected_text = corrected_text
```

## 📋 **Phase 7: Complete Migration (Week 13-14)**

### **Step 15: Final API Migration**
```python
# Create new main application
# src/main.py
from fastapi import FastAPI
from .presentation.api.controllers import consultation_controller
from .infrastructure.di.container import container_context

async def create_app() -> FastAPI:
    app = FastAPI(title="ACVA-AI v2")
    
    # Use dependency injection
    async with container_context() as container:
        app.dependency_overrides[get_container] = lambda: container
    
    # Include new controllers
    app.include_router(consultation_controller.router)
    
    return app
```

### **Step 16: Parallel Deployment**
```yaml
# docker-compose.yml - Run both versions
services:
  backend-v1:  # Current version
    build:
      context: .
      dockerfile: resources/Dockerfile
    ports:
      - "8000:8000"
    
  backend-v2:  # New version
    build:
      context: .
      dockerfile: src/infrastructure/docker/Dockerfile
    ports:
      - "8001:8000"
    
  # Load balancer to route traffic
  nginx:
    image: nginx
    ports:
      - "80:80"
    # Route /api/v2 to backend-v2, rest to backend-v1
```

## 📋 **Phase 8: Testing & Validation (Week 15-16)**

### **Step 17: Comprehensive Testing**
```python
# tests/integration/test_consultation_processing.py
import pytest
from src.application.use_cases.process_consultation_use_case import ProcessConsultationUseCase
from src.infrastructure.di.container import container_context

@pytest.mark.asyncio
async def test_consultation_processing_end_to_end():
    async with container_context() as container:
        use_case = container.get(ProcessConsultationUseCase)
        
        command = ProcessConsultationCommand(
            task_id=TaskId(uuid4()),
            audio_file=AudioFile("test.wav", ...)
        )
        
        result = await use_case.execute(command)
        
        assert result.status == ConsultationStatus.COMPLETED
        assert len(result.medications) > 0
```

### **Step 18: Performance Comparison**
```python
# scripts/performance_comparison.py
import asyncio
import time

async def compare_implementations():
    # Test old implementation
    start = time.time()
    await old_process_task(...)
    old_time = time.time() - start
    
    # Test new implementation
    start = time.time()
    await new_use_case.execute(...)
    new_time = time.time() - start
    
    print(f"Old: {old_time}s, New: {new_time}s")
```

## 📋 **Phase 9: Cleanup (Week 17-18)**

### **Step 19: Remove Old Code**
```bash
# Once new version is stable and tested
mv acva_ai/ legacy_core/
mv src/ acva_ai/

# Update imports in remaining files
find . -name "*.py" -exec sed -i 's/from acva_ai\./from legacy_core./g' {} \;
```

### **Step 20: Documentation Update**
```markdown
# Update README.md
- Architecture diagrams
- New API documentation
- Development setup guide
- Migration notes for users
```

## 🔄 **Parallel Development Strategy**

### **During Migration:**
1. **Keep both versions running** - Use feature flags or routing
2. **Gradual endpoint migration** - Migrate one endpoint at a time
3. **Shared database** - Both versions can use same data store initially
4. **Comprehensive testing** - Test both old and new implementations
5. **Monitoring** - Track performance and errors in both versions

### **Risk Mitigation:**
1. **Rollback plan** - Ability to quickly switch back to old version
2. **Data migration scripts** - For any schema changes
3. **Canary releases** - Gradually shift traffic to new version
4. **Comprehensive logging** - Track issues during migration

## 📊 **Success Metrics**

### **Technical Metrics:**
- **Code coverage** > 80%
- **Performance** maintained or improved
- **Memory usage** reduced by modular design
- **Response times** within SLA

### **Business Metrics:**
- **Zero downtime** during migration
- **No data loss** during transition
- **Feature parity** with old system
- **Improved maintainability** scores

## 🚀 **Benefits After Migration**

### **Development Benefits:**
- **Faster feature development** - Clear separation of concerns
- **Easier testing** - Mockable interfaces
- **Better code reuse** - Modular components
- **Reduced coupling** - Independent components

### **Operational Benefits:**
- **Better error handling** - Structured error management
- **Improved monitoring** - Clear boundaries for metrics
- **Easier scaling** - Independent service scaling
- **Better security** - Layered security model

This migration strategy ensures a smooth transition while maintaining system stability and allowing for continuous development. 