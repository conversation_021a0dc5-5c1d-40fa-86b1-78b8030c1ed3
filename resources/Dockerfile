# syntax=docker/dockerfile:1

FROM python:3.11-slim-bookworm AS base

# Prevents Python from writing pyc files and keeps Python from buffering stdout/stderr
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1

WORKDIR /app

# Install only necessary system dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first to leverage Docker caching
COPY resources/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application files
COPY acva_ai/ /app/acva_ai/

# Set Python path
ENV PYTHONPATH=/app


# Expose the API port
EXPOSE 8000
